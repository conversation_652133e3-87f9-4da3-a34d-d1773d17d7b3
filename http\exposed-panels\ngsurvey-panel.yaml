id: ngsurvey-panel

info:
  name: ng<PERSON>urvey Login Panel - Detect
  author: righettod
  severity: info
  description: |
    ngSurvey products was detected.
  reference:
    - https://www.ngsurvey.com/
  metadata:
    max-request: 1
    verified: true
    shodan-query: http.title:"ngSurvey enterprise survey software"
  tags: panel,ngsurvey,login,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}/home/<USER>"

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains_all(to_lower(body), "<title>ngsurvey enterprise survey software</title>", "ngsconfig.js")'
        condition: and
# digest: 4b0a00483046022100d4a4f4f96a0b6f85a9d78c9d29c6fe4fd62048de892444db31970c9f8368bf5f022100c2d871ac0687784d7113e3ec686218dafd43a74ebfcf08a3845ab15453f247e9:922c64590222798bb761d5b6d8e72950