#!/bin/bash

# CVE-2025-7775 Complete Environment Testing Script
# This script validates the entire PoC environment including:
# - Nuclei template validation
# - Docker simulation environment
# - Python exploitation tools
# - Log analysis capabilities

set -e

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              CVE-2025-7775 Complete Environment Test        ║"
echo "║                                                              ║"
echo "║  This script validates the entire PoC environment           ║"
echo "║  including template, simulation, and analysis tools         ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "${BLUE}[TEST]${NC} $test_name"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if eval "$test_command" > /dev/null 2>&1; then
        echo -e "${GREEN}[PASS]${NC} $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        echo -e "${RED}[FAIL]${NC} $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to run a test with output
run_test_with_output() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "${BLUE}[TEST]${NC} $test_name"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if eval "$test_command"; then
        echo -e "${GREEN}[PASS]${NC} $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        echo -e "${RED}[FAIL]${NC} $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

echo "Phase 1: Prerequisites Check"
echo "─────────────────────────────"

# Check if we're in the right directory
if [ ! -f "../http/cves/2025/CVE-2025-7775.yaml" ]; then
    echo -e "${RED}[ERROR]${NC} Please run this script from the CVE-2025-7775-PoC directory"
    echo "Expected directory structure:"
    echo "  nuclei-templates/"
    echo "  ├── http/cves/2025/CVE-2025-7775.yaml"
    echo "  └── CVE-2025-7775-PoC/ (current directory)"
    exit 1
fi

# Check for required tools
run_test "Docker availability" "command -v docker"
run_test "Python 3 availability" "command -v python3"
run_test "Nuclei availability" "command -v nuclei || which nuclei"

echo ""
echo "Phase 2: Nuclei Template Validation"
echo "───────────────────────────────────"

# Validate Nuclei template
run_test_with_output "Nuclei template syntax validation" "nuclei -validate -t ../http/cves/2025/CVE-2025-7775.yaml"

# Check template structure
run_test "Template file exists" "[ -f '../http/cves/2025/CVE-2025-7775.yaml' ]"
run_test "Template contains CVE ID" "grep -q 'CVE-2025-7775' ../http/cves/2025/CVE-2025-7775.yaml"
run_test "Template contains NetScaler detection" "grep -q 'NetScaler' ../http/cves/2025/CVE-2025-7775.yaml"
run_test "Template contains IPv6 detection" "grep -q 'ipv6' ../http/cves/2025/CVE-2025-7775.yaml"

echo ""
echo "Phase 3: Docker Environment Setup"
echo "─────────────────────────────────"

# Build Docker image
echo -e "${BLUE}[BUILD]${NC} Building NetScaler simulation Docker image..."
if docker build -t cve-2025-7775-sim . > build.log 2>&1; then
    echo -e "${GREEN}[PASS]${NC} Docker image built successfully"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}[FAIL]${NC} Docker image build failed"
    echo "Build log:"
    cat build.log
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Start Docker container
echo -e "${BLUE}[START]${NC} Starting NetScaler simulation container..."
if docker run -d --name cve-2025-7775-test -p 8080:80 -p 8443:443 cve-2025-7775-sim > /dev/null 2>&1; then
    echo -e "${GREEN}[PASS]${NC} Container started successfully"
    TESTS_PASSED=$((TESTS_PASSED + 1))
    
    # Wait for services to start
    echo -e "${YELLOW}[WAIT]${NC} Waiting for services to initialize..."
    sleep 15
else
    echo -e "${RED}[FAIL]${NC} Container failed to start"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo ""
echo "Phase 4: Simulation Environment Testing"
echo "──────────────────────────────────────"

# Test HTTP endpoints
run_test "NetScaler login page accessible" "curl -f -s http://localhost:8080/logon/LogonPoint/index.html > /dev/null"
run_test "NetScaler VPN page accessible" "curl -f -s http://localhost:8080/vpn/index.html > /dev/null"
run_test "NITRO API endpoint accessible" "curl -f -s http://localhost:8080/nitro/v1/config/nsversion > /dev/null"
run_test "Configuration API endpoint accessible" "curl -f -s -X POST http://localhost:8080/api/v1/configuration > /dev/null"

# Test NetScaler detection patterns
run_test "NetScaler title detection" "curl -s http://localhost:8080/logon/LogonPoint/index.html | grep -q 'NetScaler Gateway'"
run_test "NetScaler version detection" "curl -s http://localhost:8080/logon/LogonPoint/index.html | grep -q '13.1-58.15'"
run_test "NetScaler cookie detection" "curl -I http://localhost:8080/logon/LogonPoint/index.html 2>/dev/null | grep -q 'NSC_'"

echo ""
echo "Phase 5: Nuclei Template Testing"
echo "────────────────────────────────"

# Test Nuclei template against simulation
echo -e "${BLUE}[TEST]${NC} Running Nuclei template against simulation environment"
if nuclei -t ../http/cves/2025/CVE-2025-7775.yaml -target http://localhost:8080 -silent | grep -q "CVE-2025-7775"; then
    echo -e "${GREEN}[PASS]${NC} Nuclei template detected vulnerability in simulation"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}[FAIL]${NC} Nuclei template failed to detect simulation"
    echo "Debug output:"
    nuclei -t ../http/cves/2025/CVE-2025-7775.yaml -target http://localhost:8080 -debug
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo ""
echo "Phase 6: Python Tools Testing"
echo "─────────────────────────────"

# Test Python dependencies
run_test "Python requests module" "python3 -c 'import requests'"
run_test "Python urllib3 module" "python3 -c 'import urllib3'"

# Test exploitation script
if [ -f "scripts/exploit.py" ]; then
    echo -e "${BLUE}[TEST]${NC} Testing Python exploitation script"
    if python3 scripts/exploit.py -t http://localhost:8080 --timeout 10 > exploit_test.log 2>&1; then
        echo -e "${GREEN}[PASS]${NC} Python exploitation script executed successfully"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}[FAIL]${NC} Python exploitation script failed"
        echo "Script output:"
        cat exploit_test.log
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

# Test log scanner
if [ -f "scripts/log_scanner.py" ]; then
    echo -e "${BLUE}[TEST]${NC} Testing log scanner script"
    # Create a test log file
    echo '{"timestamp": "2025-01-27 10:30:45", "vulnerability_test": "CVE-2025-7775", "buffer_overflow_size": 8192}' > test.log
    if python3 scripts/log_scanner.py --log-file test.log --format json > /dev/null 2>&1; then
        echo -e "${GREEN}[PASS]${NC} Log scanner script executed successfully"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}[FAIL]${NC} Log scanner script failed"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    rm -f test.log
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

echo ""
echo "Phase 7: Cleanup"
echo "───────────────"

# Stop and remove container
echo -e "${BLUE}[CLEANUP]${NC} Stopping and removing test container..."
docker stop cve-2025-7775-test > /dev/null 2>&1 || true
docker rm cve-2025-7775-test > /dev/null 2>&1 || true

# Clean up log files
rm -f build.log exploit_test.log

echo ""
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                        TEST RESULTS                         ║"
echo "╠══════════════════════════════════════════════════════════════╣"
echo -e "║ Total Tests: ${TOTAL_TESTS}                                           ║"
echo -e "║ Passed: ${GREEN}${TESTS_PASSED}${NC}                                              ║"
echo -e "║ Failed: ${RED}${TESTS_FAILED}${NC}                                              ║"
echo "╚══════════════════════════════════════════════════════════════╝"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 ALL TESTS PASSED! The CVE-2025-7775 PoC environment is ready for submission.${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Submit the Nuclei template: http/cves/2025/CVE-2025-7775.yaml"
    echo "2. Include the complete PoC environment: CVE-2025-7775-PoC/"
    echo "3. Reference the PR template: CVE-2025-7775-PR-TEMPLATE.md"
    echo ""
    exit 0
else
    echo ""
    echo -e "${RED}❌ Some tests failed. Please review the output above and fix any issues.${NC}"
    echo ""
    exit 1
fi
