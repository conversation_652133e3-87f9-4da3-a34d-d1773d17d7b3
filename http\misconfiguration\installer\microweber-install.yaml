id: microweber-install

info:
  name: Microweber Exposed Installation - Detected
  author: pussycat0x
  severity: high
  description: |
    Microweber Installation page was exposed.
  metadata:
    verified: true
    max-request: 1
  tags: misconfig,microweber,install

http:
  - method: GET
    path:
      - '{{BaseURL}}'

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '<title>Installation</title>'
          - 'Database Server'
          - 'Login Information'
        condition: and

      - type: status
        status:
          - 200
# digest: 490a004630440220626af56a39dbe8e46c500484a95137c8cf8ec3c92a995feeabb0ac1df85d3fe302206349f6258707b10d6942d8db33681fb7550ce7a5e72c66d9659b2f6a3ec0089c:922c64590222798bb761d5b6d8e72950