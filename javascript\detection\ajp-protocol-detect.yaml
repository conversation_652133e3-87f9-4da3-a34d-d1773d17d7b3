id: ajp-protocol-detect

info:
  name: AJP Protocol Detection
  author: pussycat0x
  severity: info
  reference:
    - https://tomcat.apache.org/connectors-doc/ajp/ajpv13a.html
  metadata:
    verfied: true
    shodan-query: 'port:8009 product:"Apache Tomcat"'
    max-request: 1
  tags: js,network,detect,tomcat,apache,tcp

javascript:
  - pre-condition: |
      isPortOpen(Host,Port);
    code: |
      let packet = bytes.NewBuffer();
      let ajp_ping = "\x12\x34\x00\x01\x0a"
      data = packet.Write(ajp_ping )
      const c = require("nuclei/net");
      let conn = c.Open('tcp', `${Host}:${Port}`);
      conn.Send(data);
      let resp = conn.RecvFullString();
      // AJP messages start with "AB"
      if (resp.includes("AB\x00\x01")) {
        Export("AJP Detected");
      } else {
        conn.Close();
      }

    args:
      Host: "{{Host}}"
      Port: 8009

    extractors:
      - type: dsl
        dsl:
          - response
# digest: 4b0a00483046022100a5ce606e76e3e1490229dd59783f966947025f61f39767c8ca5ba49382a54c09022100cd4bf1f2888ba6454bc1f852857bb35f769bc699f597ac467bb378fcfd6d5724:922c64590222798bb761d5b6d8e72950