#!/usr/bin/env pwsh
# NetScaler CVE-2025-6543 Testing Automation Script
# Author: Senior Cybersecurity Engineer
# Purpose: Automated testing of Nuclei template against vulnerable NetScaler

param(
    [string]$TemplatePath = "http/cves/2025/CVE-2025-6543.yaml",
    [string]$SimulatorScript = "netscaler-simulator.py",
    [string]$NetScalerVersion = "13.1-51.15",
    [int]$WaitTime = 10
)

# Color functions for better output
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Step { param($Step, $Message) Write-Host "🔄 Step $Step`: $Message" -ForegroundColor Magenta }

Write-Host "🚀 NetScaler CVE-2025-6543 Automated Testing Workflow" -ForegroundColor Blue
Write-Host "=" * 60 -ForegroundColor Blue

# Step 1: Check Prerequisites
Write-Step 1 "Checking Prerequisites"
try {
    # Check if Docker is installed and running
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker is not installed or not in PATH"
        exit 1
    }
    Write-Success "Docker found: $dockerVersion"

    # Check if Nuclei is installed
    $nucleiVersion = nuclei -version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Nuclei is not installed or not in PATH"
        exit 1
    }
    Write-Success "Nuclei found: $nucleiVersion"

    # Check if template exists
    if (-not (Test-Path $TemplatePath)) {
        Write-Error "Template not found: $TemplatePath"
        exit 1
    }
    Write-Success "Template found: $TemplatePath"
}
catch {
    Write-Error "Prerequisites check failed: $_"
    exit 1
}

# Step 2: Clean up existing processes
Write-Step 2 "Cleaning up existing processes"
try {
    # Kill any existing Python processes running the simulator
    $pythonProcesses = Get-Process -Name "python" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*netscaler-simulator*" }
    if ($pythonProcesses) {
        Write-Info "Stopping existing simulator processes"
        $pythonProcesses | Stop-Process -Force
        Write-Success "Existing simulator processes cleaned up"
    } else {
        Write-Info "No existing simulator processes to clean up"
    }

    # Clean up any existing jobs
    $existingJobs = Get-Job | Where-Object { $_.Command -like "*netscaler-simulator*" }
    if ($existingJobs) {
        Write-Info "Cleaning up existing simulator jobs"
        $existingJobs | Stop-Job -ErrorAction SilentlyContinue
        $existingJobs | Remove-Job -Force -ErrorAction SilentlyContinue
        Write-Success "Existing jobs cleaned up"
    }
}
catch {
    Write-Warning "Process cleanup had issues, continuing..."
}

# Step 3: Check Python and Flask
Write-Step 3 "Checking Python and Flask"
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Python is not installed or not in PATH"
        exit 1
    }
    Write-Success "Python found: $pythonVersion"

    # Install Flask if not available
    Write-Info "Installing Flask if needed..."
    python -m pip install flask --quiet
    Write-Success "Flask is available"
}
catch {
    Write-Error "Failed to setup Python environment: $_"
    exit 1
}

# Step 4: Start NetScaler Simulator
Write-Step 4 "Starting NetScaler Simulator"
try {
    Write-Info "Starting Python NetScaler simulator on ports 80 and 443"
    $simulatorJob = Start-Job -ScriptBlock {
        Set-Location $using:PWD
        python netscaler-simulator.py
    }

    if ($simulatorJob) {
        Write-Success "NetScaler simulator started (Job ID: $($simulatorJob.Id))"
        $global:SimulatorJob = $simulatorJob
    } else {
        Write-Error "Failed to start simulator"
        exit 1
    }
}
catch {
    Write-Error "Failed to start NetScaler simulator: $_"
    exit 1
}

# Step 5: Wait for Simulator to Initialize
Write-Step 5 "Waiting for Simulator to Initialize"
Write-Info "NetScaler simulator needs time to start up. Waiting 10 seconds..."
$progressParams = @{
    Activity = "Initializing NetScaler Simulator"
    Status = "Please wait while simulator starts up..."
    PercentComplete = 0
}

for ($i = 1; $i -le 10; $i++) {
    $progressParams.PercentComplete = ($i / 10) * 100
    $progressParams.Status = "Waiting... $i/10 seconds"
    Write-Progress @progressParams
    Start-Sleep 1
}
Write-Progress -Activity "Initializing NetScaler Simulator" -Completed
Write-Success "Wait period completed"

# Step 6: Verify Simulator Status
Write-Step 6 "Verifying Simulator Status"
try {
    $jobStatus = Get-Job -Id $global:SimulatorJob.Id
    if ($jobStatus.State -eq "Running") {
        Write-Success "Simulator is running: $($jobStatus.State)"
    } else {
        Write-Error "Simulator is not running properly: $($jobStatus.State)"
        Receive-Job -Id $global:SimulatorJob.Id
        exit 1
    }
}
catch {
    Write-Error "Failed to verify simulator status: $_"
    exit 1
}

# Step 7: Test Simulator Connectivity
Write-Step 7 "Testing Simulator Connectivity"
$maxRetries = 5
$retryCount = 0
$connected = $false

while ($retryCount -lt $maxRetries -and -not $connected) {
    try {
        Write-Info "Testing connectivity (attempt $($retryCount + 1)/$maxRetries)..."

        # Test HTTP connectivity
        $httpResponse = Invoke-WebRequest -Uri "http://127.0.0.1" -Method Head -TimeoutSec 5 -ErrorAction Stop
        Write-Success "HTTP connectivity confirmed (Status: $($httpResponse.StatusCode))"

        # Test HTTPS connectivity (may fail due to self-signed cert, that's OK)
        try {
            $httpsResponse = Invoke-WebRequest -Uri "https://127.0.0.1" -Method Head -TimeoutSec 5 -SkipCertificateCheck -ErrorAction Stop
            Write-Success "HTTPS connectivity confirmed (Status: $($httpsResponse.StatusCode))"
        }
        catch {
            Write-Info "HTTPS test failed (expected with self-signed cert): $_"
        }

        $connected = $true
    }
    catch {
        $retryCount++
        if ($retryCount -lt $maxRetries) {
            Write-Warning "Connection attempt failed, retrying in 5 seconds... ($_)"
            Start-Sleep 5
        } else {
            Write-Error "Failed to connect to simulator after $maxRetries attempts"
            Write-Info "Simulator job output:"
            Receive-Job -Id $global:SimulatorJob.Id
            exit 1
        }
    }
}

# Step 8: Execute Nuclei Scan
Write-Step 8 "Executing Nuclei Scan"
try {
    Write-Info "Running Nuclei with template: $TemplatePath"
    Write-Info "Target: https://127.0.0.1"
    Write-Host "`n" + "="*60 + " NUCLEI SCAN RESULTS " + "="*60 -ForegroundColor Yellow
    
    # Execute Nuclei scan
    nuclei -t $TemplatePath -u "https://127.0.0.1" -debug -v
    
    $nucleiExitCode = $LASTEXITCODE
    Write-Host "`n" + "="*60 + " END SCAN RESULTS " + "="*60 -ForegroundColor Yellow
    
    if ($nucleiExitCode -eq 0) {
        Write-Success "Nuclei scan completed successfully"
    } else {
        Write-Warning "Nuclei scan completed with exit code: $nucleiExitCode"
    }
}
catch {
    Write-Error "Failed to execute Nuclei scan: $_"
}

# Step 9: Cleanup
Write-Step 9 "Cleanup"
try {
    Write-Info "Stopping NetScaler simulator"
    if ($global:SimulatorJob) {
        Stop-Job -Id $global:SimulatorJob.Id -ErrorAction SilentlyContinue
        Remove-Job -Id $global:SimulatorJob.Id -Force -ErrorAction SilentlyContinue
        Write-Success "Simulator stopped and cleaned up successfully"
    }
}
catch {
    Write-Warning "Cleanup had issues: $_"
}

Write-Host "`n🎉 NetScaler CVE Testing Automation Complete!" -ForegroundColor Green
Write-Host "Summary:" -ForegroundColor Blue
Write-Host "- Template: $TemplatePath" -ForegroundColor White
Write-Host "- Target: NetScaler Simulator (Python)" -ForegroundColor White
Write-Host "- Simulator: Stopped and cleaned up" -ForegroundColor White
