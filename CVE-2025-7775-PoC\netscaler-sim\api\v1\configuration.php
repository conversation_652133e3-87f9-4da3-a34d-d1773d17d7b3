<?php
header('Content-Type: application/json');
header('Server: NetScaler-Web/1.0');
header('Set-Cookie: NSC_AAAC=xyz123; path=/; secure');

// Simulate the vulnerable API endpoint for CVE-2025-7775
$input = file_get_contents('php://input');
$post_data = $_POST;

// Log the request for analysis
$log_entry = array(
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'uri' => $_SERVER['REQUEST_URI'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
    'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
    'content_length' => $_SERVER['CONTENT_LENGTH'] ?? 0,
    'headers' => getallheaders(),
    'post_data_size' => strlen($input),
    'vulnerability_test' => 'CVE-2025-7775'
);

// Check for buffer overflow attempt
$buffer_overflow_detected = false;
$ipv6_config_detected = false;
$command_injection_detected = false;

// Analyze the request for vulnerability indicators
if (isset($post_data['command']) && strlen($post_data['command']) > 4096) {
    $buffer_overflow_detected = true;
    $log_entry['buffer_overflow_size'] = strlen($post_data['command']);
}

if (isset($post_data['config_type']) && $post_data['config_type'] === 'ipv6_service') {
    $ipv6_config_detected = true;
}

if (isset($post_data['command']) && (
    strpos($post_data['command'], ';') !== false ||
    strpos($post_data['command'], '&&') !== false ||
    strpos($post_data['command'], '||') !== false ||
    strpos($post_data['command'], '`') !== false
)) {
    $command_injection_detected = true;
}

// Simulate vulnerable behavior
$response = array(
    'status' => 'processing',
    'message' => 'Configuration request received',
    'timestamp' => date('Y-m-d H:i:s'),
    'session_id' => 'NSC_' . bin2hex(random_bytes(8))
);

// If buffer overflow is detected, simulate the vulnerability
if ($buffer_overflow_detected && $ipv6_config_detected) {
    $response['status'] = 'error';
    $response['error_code'] = 'BUFFER_OVERFLOW_DETECTED';
    $response['message'] = 'Memory overflow in IPv6 service configuration';
    
    // Simulate memory corruption indicators
    $response['debug_info'] = array(
        'buffer_size' => strlen($post_data['command']),
        'memory_corruption' => 'DETECTED',
        'ipv6_service_affected' => true,
        'gateway_lb_impact' => true
    );
    
    // Create a test file to indicate successful exploitation simulation
    if (isset($post_data['test_marker'])) {
        $test_file = '/var/tmp/buffer_test_' . $post_data['test_marker'] . '.txt';
        file_put_contents($test_file, 'CVE-2025-7775 Buffer Overflow Test: ' . $post_data['test_marker']);
        $response['test_file_created'] = $test_file;
    }
    
    $log_entry['vulnerability_triggered'] = true;
    $log_entry['simulation_result'] = 'BUFFER_OVERFLOW_SIMULATED';
    
    // Simulate different response codes based on payload
    if (strlen($post_data['command']) > 8192) {
        http_response_code(500); // Internal Server Error
        $response['crash_simulation'] = 'SEGMENTATION_FAULT';
    } else {
        http_response_code(502); // Bad Gateway
        $response['crash_simulation'] = 'MEMORY_CORRUPTION';
    }
} else if ($command_injection_detected) {
    $response['status'] = 'error';
    $response['error_code'] = 'COMMAND_INJECTION_DETECTED';
    $response['message'] = 'Invalid command syntax detected';
    http_response_code(400);
} else {
    // Normal response for non-vulnerable requests
    $response['status'] = 'success';
    $response['message'] = 'Configuration applied successfully';
    $response['config_id'] = 'CFG_' . bin2hex(random_bytes(4));
}

// Log the complete interaction
file_put_contents('/var/log/netscaler-sim.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);

// Add response timing simulation
usleep(rand(100000, 500000)); // 0.1 to 0.5 seconds delay

echo json_encode($response, JSON_PRETTY_PRINT);
?>
