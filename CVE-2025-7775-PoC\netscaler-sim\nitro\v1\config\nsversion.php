<?php
header('Content-Type: application/json');
header('Server: NetScaler-Web/1.0');
header('Set-Cookie: NSC_AAAC=xyz123; path=/; secure');

// Simulate NetScaler NITRO API response for version information
$version_data = array(
    "nsversion" => array(
        array(
            "version" => "NetScaler 13.1-58.15",
            "buildnumber" => "13.1-58.15.nc",
            "builtdate" => "Jan 15 2025, 10:30:15",
            "builtby" => "root",
            "edition" => "Standard",
            "product" => "NetScaler",
            "built_on" => "FreeBSD 11.4-STABLE",
            "starttime" => "Mon Jan 27 08:15:32 2025",
            "lastconfigchangedtime" => "Mon Jan 27 08:15:32 2025",
            "systemtime" => "Mon Jan 27 " . date('H:i:s') . " 2025",
            "installedlicenses" => "Standard",
            "platform" => "VPX",
            "serial" => "0",
            "encryptionmode" => "FIPS",
            "fipsmode" => "ENABLED",
            "ipv6status" => "ENABLED"
        )
    ),
    "errorcode" => 0,
    "message" => "Done",
    "severity" => "NONE"
);

// Check if this is a vulnerable version (for simulation purposes)
$vulnerable_versions = array(
    "13.1-58.15", "13.1-57.28", "13.1-56.22", "13.1-55.31",
    "14.1-46.40", "14.1-45.64", "14.1-44.59", "14.1-43.10"
);

$current_version = "13.1-58.15";
$is_vulnerable = in_array($current_version, $vulnerable_versions);

// Add vulnerability indicator for testing
if ($is_vulnerable) {
    $version_data['vulnerability_status'] = 'VULNERABLE_CVE_2025_7775';
    $version_data['ipv6_services'] = 'ENABLED';
    $version_data['gateway_config'] = 'ACTIVE';
}

// Log the request for analysis
$log_entry = array(
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'uri' => $_SERVER['REQUEST_URI'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
    'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
    'version_requested' => $current_version,
    'vulnerable' => $is_vulnerable
);

file_put_contents('/var/log/netscaler-sim.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);

echo json_encode($version_data, JSON_PRETTY_PRINT);
?>
