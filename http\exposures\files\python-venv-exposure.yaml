id: python-venv-exposure

info:
  name: Python Virtual Environment  Directory Exposure
  author: a1baradi
  severity: info
  description: |
    An exposed Python virtual environment directory allows public access to internal files like pyvenv.cfg, installed packages, and configurations. This can leak sensitive information and aid attackers in identifying or exploiting vulnerabilities.
  reference:
    - https://docs.python.org/3/library/venv.html
  metadata:
    verified: true
    max-request: 1
    google-query: "intitle: Index of /venv"
  tags: venv,exposure,python-env,info

http:
  - method: GET
    path:
      - "{{BaseURL}}/venv/"

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "Index of /venv"

      - type: status
        status:
          - 200
# digest: 490a0046304402200bd2900cc49d37466b80c666a0abeee934f51aafbc161e02a1880ba507334d5702201a9056a76bd39ecebab9b7ec3f4a134c05479f8936ac80c12052bddb504f000c:922c64590222798bb761d5b6d8e72950