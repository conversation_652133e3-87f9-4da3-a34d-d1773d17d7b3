id: tixeo-panel

info:
  name: <PERSON><PERSON><PERSON> Login Panel - Detect
  author: righettod
  severity: info
  description: |
    Tixeo login panel was detected.
  reference:
    - https://www.tixeo.com/en/
  metadata:
    verified: true
    max-request: 3
    shodan-query: "http.title:\"tixeo\""
  tags: panel,tixeo,login,detect
http:
  - method: GET
    path:
      - "{{BaseURL}}/meet/services/json/v1/settings"
      - "{{BaseURL}}/meet/login.html"
      - "{{BaseURL}}/meet/"

    stop-at-first-match: true

    matchers:
      - type: dsl
        dsl:
          - 'contains(header, "Tixeo")'
          - 'contains_any(to_lower(body), "tixeo-button", "tixeoclient")'
        condition: or

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - '"applicationVersion"\s*:\s*"([0-9.]+)"'
# digest: 490a0046304402201b513c4922ff56cd5c70917992cf733d331c874a9461c70b05fdaeb2e3d51ec00220124f503d30a9d9722ad8e0b3ac1ff022fbc6307555227dbf93a7ecefd92a01b0:922c64590222798bb761d5b6d8e72950