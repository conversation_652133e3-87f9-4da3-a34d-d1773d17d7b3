id: openrouter-key

info:
  name: OpenRouter API Key
  author: mmqnym
  severity: info
  description: |
    OpenRouter API Key was detected. OpenRouter API keys provide access to various AI models and services, and unauthorized access could lead to misuse of AI resources and potential data exposure.
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N
    cvss-score: 0
    cwe-id: CWE-200
  reference:
    - https://openrouter.ai/docs/api-reference/overview
    - https://openrouter.ai/docs/api-reference/authentication
  metadata:
    verified: true
    max-request: 1
  tags: file,keys,openrouter,token,api
file:
  - extensions:
      - all

    extractors:
      - type: regex
        part: body
        regex:
          - \b(sk-or-v1-[a-fA-F0-9]{48,64})\b
# digest: 4b0a00483046022100bc3c28ef917116ca6021785c01afcaa8521b31655fb8bc2d342823d4d6bd48d5022100c78fa00a6349d1f65f7ba18096e03e0a683e4881a2bf71ae870f267adb3064d2:922c64590222798bb761d5b6d8e72950