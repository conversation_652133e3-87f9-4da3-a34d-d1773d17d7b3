id: magicinfo-config-file

info:
  name: Samsung MagicINFO Configuration File
  author: s4e-io
  severity: info
  description: |
    Detects exposure of Samsung MagicINFO configuration file (/MagicInfo/config.js),and extracts magicInfoFrontEndVersion.
  reference:
    - https://www.samsung.com/de/business/display-solutions/magicinfo/
  metadata:
    vendor: samsung
    product: magicinfo_9_server
    shodan-query: title:"MagicINFO"
    fofa-query: title:"MagicINFO"
  tags: config,exposure,magicinfo

http:
  - method: GET
    path:
      - "{{BaseURL}}/MagicInfo/config.js"

    matchers:
      - type: dsl
        dsl:
          - 'contains_all(body, "globalConfig", "MagicINFO", "samsung")'

    extractors:
      - type: regex
        name: magicinfo_version
        group: 1
        part: body
        regex:
          - '"magicInfoFrontEndVersion":\s*"([^"]+)"'
# digest: 490a0046304402203b65b0f463106e7b4024af3fa347167f65be64a2b9ec20312f362d76ad9ecaa302207042dd3750edf006f1953a30448e505f0baa07647361b29d2fd874b77735cf3f:922c64590222798bb761d5b6d8e72950