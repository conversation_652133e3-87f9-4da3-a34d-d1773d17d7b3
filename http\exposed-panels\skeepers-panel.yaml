id: skeepers-panel

info:
  name: Skeepers Login Panel - Detect
  author: righettod
  severity: info
  description: |
    Skeepers login panel was detected.
  reference:
    - https://skeepers.io
  metadata:
    verified: true
    max-request: 2
    shodan-query: http.title:"Skeepers"
  tags: panel,skeepers,login,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}/backend/login"
      - "{{BaseURL}}"

    stop-at-first-match: true

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains(to_lower(body), "skeepers") && contains(to_lower(body), "login")'
        condition: and

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - 'Version\s+([0-9\.]+)\s+-'
# digest: 4b0a00483046022100a0c98c104644b3df5b2b2cc473f921ed5867a9471b6670b4ecc9c8484f3d81e2022100b13bab0d935075ac860a959f78e4597353895ccbbbab85c731d67693d955e7c1:922c64590222798bb761d5b6d8e72950