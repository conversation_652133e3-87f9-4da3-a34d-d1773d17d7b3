id: composer-config

info:
  name: Composer Config - Detect
  author: <PERSON><PERSON><PERSON> (Mah3Sec_),TheZakMan
  severity: info
  description: Composer configuration file detected.
  reference: https://getcomposer.org/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N
    cvss-score: 0
    cwe-id: CWE-200
  metadata:
    max-request: 4
  tags: config,exposure

http:
  - method: GET
    path:
      - "{{BaseURL}}/composer.json"
      - "{{BaseURL}}/composer.lock"
      - "{{BaseURL}}/.composer/composer.json"
      - "{{BaseURL}}/vendor/composer/installed.json"

    matchers:
      - type: dsl
        name: composer.lock
        dsl:
          - "contains_all(body, 'packages', 'getcomposer.org') && status_code == 200"
          - "contains_any(tolower(header), 'application/octet-stream', 'application/json', 'text/plain')"
        condition: and

      - type: dsl
        name: composer.json
        dsl:
          - "contains_all(body, 'require', 'autoload') && contains(tolower(header), 'application/json') && status_code == 200"
# digest: 4b0a00483046022100bdafbf89f8e0fee0320a11b0d4c53d09934bbdc0a9241ba63bf2d520988911aa022100a387cecebb076e0ab86ccfae799e96663912cbf9ec1e70412584cdf54477dc1c:922c64590222798bb761d5b6d8e72950