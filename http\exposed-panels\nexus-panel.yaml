id: nexus-panel

info:
  name: Nexus Login Panel - Detect
  author: righettod
  severity: info
  description: |
    Nexus login panel was detected.
  reference:
    - https://www.sonatype.com/products/sonatype-nexus-repository
  metadata:
    verified: true
    max-request: 1
    shodan-query: http.title:"Sonatype Nexus Repository"
  tags: panel,nexus,login,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}"

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains_any(to_lower(body), "<title>sonatype nexus repository", "content=\"sonatype nexus repository", "nexus-coreui-bundle")'
        condition: and

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - '_v=([0-9\.\-]+)'
# digest: 4a0a00473045022100b33d27d53826d1cd08fd8281f0d5fecbfe2303ce7ce7acf4d555cf6a325cc4b102205c26347c6a73584f93564bfc9f884fed9c371898016f1f383a2f8c4920b7b333:922c64590222798bb761d5b6d8e72950