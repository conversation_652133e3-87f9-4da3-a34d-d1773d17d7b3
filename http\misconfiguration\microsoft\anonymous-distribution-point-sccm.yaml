id: anonymous-distribution-point-sccm

info:
  name: Microsoft SCCM - Anonymous Distribution Point Access
  author: matejsmycka
  severity: medium
  description: |
    Microsoft System Center Configuration Manager (SCCM) can be configured to allow anonymous access to its distribution points.This can lead to sensitive data exposure and information gathering by unauthorized users.This misconfiguration is exploitable only via HTTP.
  reference:
    - https://www.synacktiv.com/en/publications/sccmsecretspy-exploiting-sccm-policies-distribution-for-credentials-harvesting-initial
    - https://github.com/badsectorlabs/sccm-http-looter
    - https://learn.microsoft.com/en-us/intune/configmgr/core/servers/deploy/configure/install-and-configure-distribution-points
  tags: misconfig,microsoft,sccm,anonymous,distribution-point

http:
  - method: GET
    path:
      - "{{BaseURL}}/SMS_DP_SMSPKG$/Datalib"
      - "{{BaseURL}}:80/SMS_DP_SMSPKG$/Datalib"

    stop-at-first-match: true
    matchers-condition: and
    matchers:
      - type: regex
        regex:
          - '/SMS_DP_SMSPKG\$\/Datalib/([0-9a-z-]+)\.INI'

      - type: status
        status:
          - 200
# digest: 490a00463044022055390a861b04787d28b1684c05f919ffc4f948c386143cf472b532a6c248ee31022063031dcc4e86c377430f4b9efb312c8b3a1cfc7a7ec6a37b50b36543188dfd2a:922c64590222798bb761d5b6d8e72950