id: unleash-panel

info:
  name: Unleash Panel - Detect
  author: userdehghani
  severity: info
  description: |
    Open-source feature management solution built for developers.
  reference:
    - https://www.getunleash.io/
  metadata:
    verified: true
    max-request: 3
    shodan-query: http.favicon.hash:-608690655
  tags: panel,unleash,login,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}/login"
      - "{{BaseURL}}/sign-in"
      - "{{BaseURL}}/favicon.ico"

    stop-at-first-match: true
    matchers-condition: or
    matchers:
      - type: word
        part: body
        words:
          - '<title>Unleash'
          - 'content="unleash'
          - 'alt="getunleash'
          - 'Sign-in - Unleash hosted'
        case-insensitive: true
        condition: or

      - type: dsl
        dsl:
          - "status_code==200 && (\"-608690655\" == mmh3(base64_py(body)))"
# digest: 4a0a00473045022100a539d6845052a7b15a53cdab2d840a0b89bf158e013dbd7c22537ec17490bf4202200c836fa01b1386a4b4a6144c3a858a008564a2032c7c6717d9854e8e4851632d:922c64590222798bb761d5b6d8e72950