id: swagger-api

info:
  name: Public Swagger API - Detect
  author: pdteam,c-sh0,AmirH<PERSON><PERSON>isi
  severity: info
  description: Public Swagger API was detected.
  reference: https://swagger.io/
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N
    cwe-id: CWE-200
  metadata:
    verified: true
    max-request: 59
    shodan-query: "http.title:\"swagger\""
  tags: exposure,api,swagger

http:
  - method: GET
    path:
      - "{{BaseURL}}{{paths}}"

    payloads:
      paths:
        - "/swagger-ui/swagger-ui.js"
        - "/swagger/swagger-ui.js"
        - "/swagger-ui.js"
        - "/swagger/ui/swagger-ui.js"
        - "/swagger/ui/index"
        - "/swagger/index.html"
        - "/swagger-ui.html"
        - "/swagger/swagger-ui.html"
        - "/api/swagger-ui.html"
        - "/api-docs/swagger.json"
        - "/api-docs/swagger.yaml"
        - "/api_docs"
        - "/swagger.json"
        - "/swagger.yaml"
        - "/swagger/v1/swagger.json"
        - "/swagger/v1/swagger.yaml"
        - "/api/index.html"
        - "/api/doc"
        - "/api/docs/"
        - "/api/swagger.json"
        - "/api/swagger.yaml"
        - "/api/swagger.yml"
        - "/api/swagger/index.html"
        - "/api/swagger/swagger-ui.html"
        - "/api/api-docs/swagger.json"
        - "/api/api-docs/swagger.yaml"
        - "/api/swagger-ui/swagger.json"
        - "/api/swagger-ui/swagger.yaml"
        - "/api/apidocs/swagger.json"
        - "/api/apidocs/swagger.yaml"
        - "/api/swagger-ui/api-docs"
        - "/api/doc.json"
        - "/api/api-docs"
        - "/api/apidocs"
        - "/api/swagger"
        - "/api/swagger/static/index.html"
        - "/api/swagger-resources"
        - "/api/swagger-resources/restservices/v2/api-docs"
        - "/api/__swagger__/"
        - "/api/_swagger_/"
        - "/api/spec/swagger.json"
        - "/api/spec/swagger.yaml"
        - "/api/swagger/ui/index"
        - "/__swagger__/"
        - "/_swagger_/"
        - "/api/v1/swagger-ui/swagger.json"
        - "/api/v1/swagger-ui/swagger.yaml"
        - "/swagger-resources/restservices/v2/api-docs"
        - "/api/swagger_doc.json"
        - "/docu"
        - "/docs"
        - "/docs/"
        - "/swagger"
        - "/api-doc"
        - "/doc/"
        - "/swagger-ui/springfox.js"
        - "/swagger-ui/swagger-ui-standalone-preset.js"
        - "/swagger-ui/swagger-ui/swagger-ui-bundle.js"
        - "/webjars/swagger-ui/swagger-ui-bundle.js"
        - "/webjars/swagger-ui/index.html"
        - "/developers/documentation"

    headers:
      Accept: text/html
    stop-at-first-match: true

    matchers-condition: and
    matchers:
      - type: word
        words:
          - "swagger:"
          - "Swagger 2.0"
          - "\"swagger\":"
          - "Swagger UI"
          - "loadSwaggerUI"
          - "**token**:"
          - "id=\"swagger-ui"
        condition: or

      - type: status
        status:
          - 200

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - " @version (v[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3})"
# digest: 4b0a004830460221009b9f635954985aa3ec4b9adf63036b03804ea8e1574c05f5d52661232b6ab654022100e342acdfdac142cfe29f08db5e3247ee59f4e6b92c34ce9f77559e9cfdb8de30:922c64590222798bb761d5b6d8e72950