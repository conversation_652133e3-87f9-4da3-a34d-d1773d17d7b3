# Dockerfile for CVE-2024-35286 Mock Environment
FROM ubuntu:20.04

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install required packages
RUN apt-get update && apt-get install -y \
    apache2 \
    php7.4 \
    php7.4-mysql \
    mysql-server \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create directory structure to mimic Mitel MiCollab
RUN mkdir -p /var/www/html/npm-pwg
RUN mkdir -p /var/www/html/npm-admin

# Copy mock application files
COPY mock-app/ /var/www/html/

# Configure Apache to mimic the path normalization vulnerability
COPY apache-config/000-default.conf /etc/apache2/sites-available/000-default.conf
COPY apache-config/apache2.conf /etc/apache2/apache2.conf

# Enable Apache modules
RUN a2enmod rewrite
RUN a2enmod proxy
RUN a2enmod proxy_http

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html
RUN chmod -R 755 /var/www/html

# Initialize MySQL database
COPY db-init/init.sql /docker-entrypoint-initdb.d/

# Expose port 80
EXPOSE 80

# Start services
COPY start-services.sh /start-services.sh
RUN chmod +x /start-services.sh

CMD ["/start-services.sh"]
