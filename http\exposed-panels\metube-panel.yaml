id: metube-detect

info:
  name: MeTube Instance Detected
  author: rxerium
  severity: info
  description: |
    A MeTube instance was detected.
  reference:
    - https://github.com/alexta69/metube
  metadata:
    verified: true
    shodan-query: http.title:MeTube
    max-request: 1
  tags: metube,detect,panel,login

http:
  - method: GET
    path:
      - "{{BaseURL}}"

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "<title>MeTube</title>"

      - type: status
        status:
          - 200
# digest: 4a0a00473045022059c593963a0f2669c4082e73cb06acf18a280ec7a650b2496643deb3d003a1f5022100de3f1db68f354714ec459848dd71f015655426bbf04f366c66ffc45103530560:922c64590222798bb761d5b6d8e72950