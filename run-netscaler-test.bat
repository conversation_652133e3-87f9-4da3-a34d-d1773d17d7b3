@echo off
echo ========================================
echo NetScaler CVE-2025-6543 Test Automation
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.x and try again
    pause
    exit /b 1
)

REM Check if Nuclei is available
nuclei -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Nuclei is not installed or not in PATH
    echo Please install Nuclei and try again
    pause
    exit /b 1
)

REM Install Flask if needed
echo Installing Flask...
python -m pip install flask --quiet

REM Run the PowerShell automation script
echo Starting automation...
powershell -ExecutionPolicy Bypass -File "netscaler-cve-test-automation.ps1"

echo.
echo Test completed. Press any key to exit...
pause >nul
