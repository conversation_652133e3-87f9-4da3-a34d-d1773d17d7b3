# CVE-2025-7775 NetScaler ADC/Gateway Simulation Environment
# This creates a simulated vulnerable NetScaler environment for testing purposes
FROM ubuntu:20.04

LABEL maintainer="CVE-2025-7775-PoC"
LABEL description="Simulated NetScaler ADC/Gateway environment for CVE-2025-7775 testing"

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install required packages
RUN apt-get update && apt-get install -y \
    apache2 \
    php7.4 \
    php7.4-cli \
    php7.4-common \
    php7.4-curl \
    php7.4-json \
    python3 \
    python3-pip \
    curl \
    wget \
    net-tools \
    iputils-ping \
    vim \
    nginx \
    openssl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip3 install flask requests

# Create NetScaler simulation directories
RUN mkdir -p /var/www/netscaler/{logon/LogonPoint,nitro/v1/config,api/v1,vpn,var/tmp}

# Copy NetScaler simulation files
COPY netscaler-sim/ /var/www/netscaler/
COPY scripts/ /opt/scripts/
COPY favicon.ico /var/www/netscaler/

# Set up Apache configuration for NetScaler simulation
COPY apache-netscaler.conf /etc/apache2/sites-available/netscaler.conf
RUN a2ensite netscaler.conf && a2dissite 000-default.conf
RUN a2enmod rewrite headers ssl

# Generate self-signed SSL certificate
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/private/netscaler.key \
    -out /etc/ssl/certs/netscaler.crt \
    -subj "/C=US/ST=CA/L=San Francisco/O=NetScaler Simulation/CN=netscaler.local"

# Set proper permissions
RUN chown -R www-data:www-data /var/www/netscaler
RUN chmod +x /opt/scripts/*.py

# Expose ports
EXPOSE 80 443 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/logon/LogonPoint/index.html || exit 1

# Start services
CMD ["/opt/scripts/start-services.sh"]