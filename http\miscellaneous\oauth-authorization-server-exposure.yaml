id: oauth-authorization-server-exposure

info:
  name: Well-Known OAuth Authorization Server Metadata
  author: rxeriums
  severity: info
  description: |
    Detects OAuth 2.0 Authorization Server metadata (RFC 8414).
  impact: |
    Presence of this well-known resource can expose implementation details or policies.
  reference:
    - https://www.rfc-editor.org/rfc/rfc8414
  metadata:
    verified: true
    max-request: 1
    google-query: inurl:"/.well-known/oauth-authorization-server"
  tags: well-known,oauth,oidc,security,rfc8414,miscellaneous,misc

http:
  - method: GET
    path:
      - "{{BaseURL}}/.well-known/oauth-authorization-server"

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "issuer"
          - "authorization_endpoint"
        condition: and

      - type: status
        status:
          - 200
# digest: 4a0a00473045022015612b7d6c74d613029dd0673e72668fa302e804393ffc3a5c7433b786913068022100d86161cf339d0e260389e4ef1753c6d6b044c234b517a1cd42d6da37041bf953:922c64590222798bb761d5b6d8e72950