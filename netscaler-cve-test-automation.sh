#!/bin/bash
# NetScaler CVE-2025-6543 Testing Automation Script
# Author: Senior Cybersecurity Engineer
# Purpose: Automated testing of Nuclei template against vulnerable NetScaler

set -e

# Configuration
TEMPLATE_PATH="${1:-http/cves/2025/CVE-2025-6543.yaml}"
CONTAINER_NAME="vulnerable-netscaler"
NETSCALER_VERSION="13.1-51.15"
WAIT_TIME=120

# Color functions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

success() { echo -e "${GREEN}✅ $1${NC}"; }
info() { echo -e "${CYAN}ℹ️  $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; }
step() { echo -e "${MAGENTA}🔄 Step $1: $2${NC}"; }

echo -e "${BLUE}🚀 NetScaler CVE-2025-6543 Automated Testing Workflow${NC}"
echo -e "${BLUE}============================================================${NC}"

# Step 1: Check Prerequisites
step 1 "Checking Prerequisites"
if ! command -v docker &> /dev/null; then
    error "Docker is not installed or not in PATH"
    exit 1
fi
success "Docker found: $(docker --version)"

if ! command -v nuclei &> /dev/null; then
    error "Nuclei is not installed or not in PATH"
    exit 1
fi
success "Nuclei found: $(nuclei -version 2>/dev/null | head -1)"

if [ ! -f "$TEMPLATE_PATH" ]; then
    error "Template not found: $TEMPLATE_PATH"
    exit 1
fi
success "Template found: $TEMPLATE_PATH"

# Step 2: Clean up existing container
step 2 "Cleaning up existing containers"
if docker ps -a --filter "name=$CONTAINER_NAME" --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
    info "Stopping and removing existing container: $CONTAINER_NAME"
    docker stop $CONTAINER_NAME >/dev/null 2>&1 || true
    docker rm $CONTAINER_NAME >/dev/null 2>&1 || true
    success "Existing container cleaned up"
else
    info "No existing container to clean up"
fi

# Step 3: Pull Docker Image
step 3 "Pulling NetScaler Docker Image"
info "Pulling quay.io/citrix/citrix-adc-cpx:$NETSCALER_VERSION"
if docker pull "quay.io/citrix/citrix-adc-cpx:$NETSCALER_VERSION"; then
    success "Docker image pulled successfully"
else
    error "Failed to pull Docker image"
    exit 1
fi

# Step 4: Run Docker Container
step 4 "Starting NetScaler Container"
info "Starting container with name: $CONTAINER_NAME"
CONTAINER_ID=$(docker run -dt \
    --name $CONTAINER_NAME \
    --cap-add=NET_ADMIN \
    -p 80:80 \
    -p 443:443 \
    -p 9080:9080 \
    -e EULA=yes \
    "quay.io/citrix/citrix-adc-cpx:$NETSCALER_VERSION")

if [ $? -eq 0 ]; then
    success "Container started with ID: ${CONTAINER_ID:0:12}"
else
    error "Failed to start container"
    exit 1
fi

# Step 5: Wait for NetScaler to Initialize
step 5 "Waiting for NetScaler to Initialize"
info "NetScaler needs time to start up completely. Waiting $WAIT_TIME seconds..."
for i in $(seq 1 $WAIT_TIME); do
    printf "\rWaiting... %d/%d seconds" $i $WAIT_TIME
    sleep 1
done
echo ""
success "Wait period completed"

# Step 6: Verify Container Status
step 6 "Verifying Container Status"
CONTAINER_STATUS=$(docker ps --filter "name=$CONTAINER_NAME" --format "{{.Status}}")
if [[ $CONTAINER_STATUS == *"Up"* ]]; then
    success "Container is running: $CONTAINER_STATUS"
else
    error "Container is not running properly"
    docker logs $CONTAINER_NAME --tail 20
    exit 1
fi

# Step 7: Test NetScaler Connectivity
step 7 "Testing NetScaler Connectivity"
MAX_RETRIES=10
RETRY_COUNT=0
CONNECTED=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$CONNECTED" = false ]; do
    info "Testing connectivity (attempt $((RETRY_COUNT + 1))/$MAX_RETRIES)..."
    
    # Test HTTP connectivity
    if curl -s -I --max-time 10 "http://127.0.0.1" >/dev/null 2>&1; then
        success "HTTP connectivity confirmed"
        
        # Test HTTPS connectivity
        if curl -s -I -k --max-time 10 "https://127.0.0.1" >/dev/null 2>&1; then
            success "HTTPS connectivity confirmed"
            CONNECTED=true
        fi
    fi
    
    if [ "$CONNECTED" = false ]; then
        RETRY_COUNT=$((RETRY_COUNT + 1))
        if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
            warning "Connection attempt failed, retrying in 10 seconds..."
            sleep 10
        else
            error "Failed to connect to NetScaler after $MAX_RETRIES attempts"
            info "Container logs:"
            docker logs $CONTAINER_NAME --tail 20
            exit 1
        fi
    fi
done

# Step 8: Execute Nuclei Scan
step 8 "Executing Nuclei Scan"
info "Running Nuclei with template: $TEMPLATE_PATH"
info "Target: https://127.0.0.1"
echo -e "${YELLOW}============================================================ NUCLEI SCAN RESULTS ============================================================${NC}"

# Execute Nuclei scan
nuclei -t "$TEMPLATE_PATH" -u "https://127.0.0.1" -debug -v
NUCLEI_EXIT_CODE=$?

echo -e "${YELLOW}============================================================ END SCAN RESULTS ============================================================${NC}"

if [ $NUCLEI_EXIT_CODE -eq 0 ]; then
    success "Nuclei scan completed successfully"
else
    warning "Nuclei scan completed with exit code: $NUCLEI_EXIT_CODE"
fi

# Step 9: Cleanup
step 9 "Cleanup"
info "Stopping and removing container: $CONTAINER_NAME"
docker stop $CONTAINER_NAME >/dev/null 2>&1 || true
docker rm $CONTAINER_NAME >/dev/null 2>&1 || true
success "Container cleaned up successfully"

echo ""
echo -e "${GREEN}🎉 NetScaler CVE Testing Automation Complete!${NC}"
echo -e "${BLUE}Summary:${NC}"
echo -e "${NC}- Template: $TEMPLATE_PATH${NC}"
echo -e "${NC}- Target: NetScaler CPX $NETSCALER_VERSION${NC}"
echo -e "${NC}- Container: $CONTAINER_NAME (cleaned up)${NC}"
