id: safenet-authentication-panel

info:
  name: SafeNet Authentication Login Panel - Detect
  author: righettod
  severity: info
  description: |
    SafeNet Authentication Service Self Enrollment login panel was detected.
  reference:
    - https://cpl.thalesgroup.com/access-management/safenet-trusted-access
  metadata:
    verified: true
    max-request: 1
    shodan-query: http.title:"Self Enrollment"
  tags: panel,safenet,thales,login,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}/selfenrollment/Enrollment.aspx"

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains(to_lower(body), "self enrollment") && contains_any(to_lower(body), "safenet", "thales")'
        condition: and

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - 'V=([0-9.]+)'
# digest: 4a0a00473045022100a6586e5607c9ffe73668224dfb2f74900efed76c383f34dd95d28a0d798502d7022026c4ca0cd635c6bf773289a4519987067b0442a566551163fdc2120a5926dd6f:922c64590222798bb761d5b6d8e72950