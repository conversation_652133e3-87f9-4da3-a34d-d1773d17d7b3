#!/usr/bin/env python3
"""
Mock Mitel MiCollab server for CVE-2024-35286 validation
Simulates the vulnerable /np-app/api/d-vmail/get-msg-status endpoint
"""

from flask import Flask, request, jsonify
import time
import re
import logging

app = Flask(__name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.route('/np-app/api/d-vmail/get-msg-status', methods=['POST'])
def get_msg_status():
    """
    Vulnerable endpoint that simulates CVE-2024-35286
    Checks for SQL injection payloads in msgId parameter and introduces time delay
    """
    logger.info(f"Received POST request to /np-app/api/d-vmail/get-msg-status")
    logger.info(f"Content-Type: {request.content_type}")
    logger.info(f"Form data: {request.form}")
    
    # Get the msgId parameter from form data
    msg_id = request.form.get('msgId', '')
    logger.info(f"msgId parameter: {msg_id}")
    
    # Check if the parameter contains SQL injection payload with sleep function
    if re.search(r'sleep\s*\(\s*\d+\s*\)', msg_id, re.IGNORECASE):
        logger.info("SQL injection payload detected - introducing time delay")
        
        # Extract sleep duration from payload
        match = re.search(r'sleep\s*\(\s*(\d+)\s*\)', msg_id, re.IGNORECASE)
        if match:
            sleep_duration = int(match.group(1))
            logger.info(f"Sleeping for {sleep_duration} seconds to simulate SQL injection")
            time.sleep(sleep_duration)
        else:
            # Default sleep if we can't parse the duration
            logger.info("Sleeping for default 5 seconds")
            time.sleep(5)
    
    # Return successful response
    response = {
        "status": "success",
        "message": "Message status retrieved",
        "timestamp": int(time.time())
    }
    
    logger.info(f"Returning response: {response}")
    return jsonify(response), 200

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "mitel-micollab-mock"}), 200

@app.route('/', methods=['GET'])
def index():
    """Root endpoint"""
    return jsonify({
        "service": "Mitel MiCollab Mock Server",
        "version": "1.0.0",
        "vulnerable_endpoint": "/np-app/api/d-vmail/get-msg-status",
        "cve": "CVE-2024-35286"
    }), 200

if __name__ == '__main__':
    logger.info("Starting Mitel MiCollab Mock Server for CVE-2024-35286 validation")
    app.run(host='0.0.0.0', port=5000, debug=True)
