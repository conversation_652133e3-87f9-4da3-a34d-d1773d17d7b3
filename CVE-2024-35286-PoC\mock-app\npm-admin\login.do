<?php
// Mock Mitel MiCollab npm-admin login endpoint
// This simulates the vulnerable SQL injection point in CVE-2024-35286

header('Content-Type: text/html; charset=UTF-8');

// Simulate database connection
$host = 'localhost';
$dbname = 'micollab';
$username = 'micollab_user';
$password = 'micollab_pass';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    // Fallback for demo purposes
    error_log("Database connection failed: " . $e->getMessage());
}

// Get POST parameters
$subAction = $_POST['subAction'] ?? '';
$user = $_POST['username'] ?? '';
$pass = $_POST['password'] ?? '';
$clusterIndex = $_POST['clusterIndex'] ?? '';

// Log the request for debugging
error_log("CVE-2024-35286 Mock: Received login attempt - User: $user, SubAction: $subAction");

if ($subAction === 'basicLogin') {
    // Vulnerable SQL query - directly concatenating user input
    // This simulates the actual vulnerability in Mitel MiCollab
    $sql = "SELECT * FROM users WHERE username = '$user' AND password = '$pass'";
    
    error_log("CVE-2024-35286 Mock: Executing SQL: $sql");
    
    // Check if this looks like a SQL injection attempt
    if (preg_match('/sleep\s*\(\s*\d+\s*\)/i', $user) || 
        preg_match('/pg_sleep\s*\(\s*\d+\s*\)/i', $user) ||
        preg_match('/waitfor\s+delay/i', $user)) {
        
        error_log("CVE-2024-35286 Mock: SQL injection detected in username parameter");
        
        // Extract sleep duration from the payload
        if (preg_match('/(?:pg_)?sleep\s*\(\s*(\d+)\s*\)/i', $user, $matches)) {
            $sleepDuration = intval($matches[1]);
            error_log("CVE-2024-35286 Mock: Simulating SQL injection with sleep($sleepDuration)");
            
            // Simulate the time delay that would occur in a real SQL injection
            sleep($sleepDuration);
        }
    }
    
    // Return a generic response
    echo json_encode([
        'status' => 'error',
        'message' => 'Invalid credentials',
        'timestamp' => time()
    ]);
} else {
    // Return login form
    echo '<!DOCTYPE html>
<html>
<head>
    <title>MiCollab NPM Admin Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; }
        .login-form { max-width: 400px; margin: 0 auto; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; margin: 5px 0; }
        input[type="submit"] { background: #007cba; color: white; padding: 10px 20px; border: none; }
    </style>
</head>
<body>
    <div class="login-form">
        <h2>MiCollab NPM Administration</h2>
        <form method="POST" action="/npm-admin/login.do">
            <input type="hidden" name="subAction" value="basicLogin">
            <input type="text" name="username" placeholder="Username" required>
            <input type="password" name="password" placeholder="Password" required>
            <input type="hidden" name="clusterIndex" value="0">
            <input type="submit" value="Login">
        </form>
    </div>
</body>
</html>';
}
?>
