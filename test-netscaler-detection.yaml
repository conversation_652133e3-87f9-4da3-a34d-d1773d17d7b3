id: test-netscaler-detection

info:
  name: Test NetScaler Detection
  author: test
  severity: info
  description: Test template to verify NetScaler detection
  tags: test,netscaler

http:
  - raw:
      - |
        GET /favicon.ico HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

      - |
        GET /logon/LogonPoint/index.html HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

    matchers:
      - type: dsl
        name: netscaler_detected
        dsl:
          - "contains(body_2, 'NetScaler') || contains(body_2, 'Citrix') || contains(header_2, 'NSC_') || contains(body_1, 'NetScaler')"

    extractors:
      - type: regex
        part: body_1,body_2,header_1,header_2
        name: debug_info
        regex:
          - "(NetScaler[^\\s]*)"
          - "(NSC_[^;]*)"
          - "(Server: [^\\r\\n]*)"
