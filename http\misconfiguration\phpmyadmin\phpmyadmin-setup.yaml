id: phpmyadmin-setup

info:
  name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Setup File - Detect
  author: <PERSON><PERSON><PERSON><PERSON><PERSON>,the<PERSON><PERSON><PERSON>er,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,0xpugal
  severity: medium
  description: Multiple phpMyAdmin setup files were detected.
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
    cvss-score: 5.3
    cwe-id: CWE-200
    cpe: cpe:2.3:a:phpmyadmin:phpmyadmin:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 16
    shodan-query: http.html:"phpMyAdmin"
    product: phpmyadmin
    vendor: phpmyadmin
  tags: phpmyadmin,misconfig

http:
  - method: GET
    path:
      - "{{BaseURL}}{{paths}}"
    payloads:
      paths:
        - "/phpmyadmin/scripts/setup.php"
        - "/phpMyAdmin/scripts/setup.php"
        - "/_phpmyadmin/scripts/setup.php"
        - "/forum/phpmyadmin/scripts/setup.php"
        - "/php/phpmyadmin/scripts/setup.php"
        - "/typo3/phpmyadmin/scripts/setup.php"
        - "/web/phpmyadmin/scripts/setup.php"
        - "/xampp/phpmyadmin/scripts/setup.php"
        - "/sysadmin/phpMyAdmin/scripts/setup.php"
        - "/phpmyadmin/setup/index.php"
        - "/phpMyAdmin/setup/index.php"
        - "/pma/setup/index.php"
        - "/admin/pma/setup/index.php"
        - "/phpmyadmin/setup/"
        - "/setup/index.php"
        - "/admin/"
        - "/phpMyAdminOLD/setup/index.php"

    stop-at-first-match: true

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "You want to configure phpMyAdmin using web interface"
          - "<title>phpMyAdmin setup</title>"
        condition: or

      - type: status
        status:
          - 200
# digest: 4a0a00473045022100cd0c46c2350820b2657bae550b6baa5c7b455618af9cd943d35639caf2ca23c2022013290fa9427105903bc04fb20af0fe809e72a90707939c948db3c8e9c8f21608:922c64590222798bb761d5b6d8e72950