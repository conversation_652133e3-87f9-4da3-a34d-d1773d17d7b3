id: unigui-server-monitor-exposure

info:
  name: UniGUI Server Monitor Panel - Exposure
  author: serrapa
  severity: low
  description: |
    Detects exposed UniGUI Server Monitor Panels which could reveal sensitive server statistics, users sessions, licensing information and others data.
  reference:
    - https://www.unigui.com/doc/online_help/using-server-monitor-(server-c.htm
  metadata:
    verified: true
    max-request: 1
    shodan-query: title:"uniGUI"
    fofa-query: title="uniGUI"
  tags: exposure,unigui,misconfig

http:
  - method: GET
    path:
      - "{{BaseURL}}/server"

    matchers-condition: and
    matchers:
      - type: dsl
        dsl:
          - 'contains_any(body, "uniGUI Standalone Server", "uniGUI License Information", "Server Statistics")'
          - 'status_code == 200'
        condition: and

      - type: dsl
        dsl:
          - 'contains(body, "layout:\"fit\",title:\"uniGUI Standalone Server\"")'
          - 'contains(body, "layout:\"absolute\",title:\"Server Statistics\"")'
        condition: or
# digest: 490a0046304402202fda180fe991f30e86520a08bd54992065b10c5654c7954bc1b7e93b3e3116d60220429a25b1448e7a4db5ce345ac36d51df32882dfc49e24c96fe59b8187b493d7e:922c64590222798bb761d5b6d8e72950