id: CVE-2025-32970

info:
  name: XWiki WYSIWYG API - Open Redirect
  author: ritikchaddha
  severity: medium
  description: |
    A vulnerability in XWiki's WYSIWYG API allows an attacker to redirect users to arbitrary external URLs through the xerror parameter. This could be used in phishing attacks to redirect users to malicious websites.
  reference:
    - https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-pjhg-9wr9-rj96
    - https://nvd.nist.gov/vuln/detail/CVE-2025-32970
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N
    cvss-score: 5.4
    cwe-id: CWE-601
    cpe: cpe:2.3:a:xwiki:xwiki:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    verified: true
    vendor: xwiki
    product: xwiki
    shodan-query: html:"data-xwiki-reference"
    fofa-query: body="data-xwiki-reference"
  tags: cve,cve2025,xwiki,redirect

http:
  - method: GET
    path:
      - "{{BaseURL}}/xwiki/bin/view/Main/?foo=bar&foo_syntax=invalid&RequiresHTMLConversion=foo&xerror=https://oast.me"

    matchers-condition: and
    matchers:
      - type: regex
        part: header
        regex:
          - '(?m)^(?:Location\s*?:\s*?)(?:https?:\/\/|\/\/|\/\\\\|\/\\)?(?:[a-zA-Z0-9\-_\.@]*)oast\.me.*$'

      - type: word
        part: header
        words:
          - text/javascript
# digest: 4a0a00473045022100ec12db17f5a22d27a5251eccd3dab079e3c1d38e4994e33165fb107e33f18e19022052cd61effe4090cd2591d87d72db092f1438454ce51b64470bc0e4f93412aa53:922c64590222798bb761d5b6d8e72950