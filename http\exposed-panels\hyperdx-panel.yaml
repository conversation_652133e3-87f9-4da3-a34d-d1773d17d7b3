id: hyperdx-panel

info:
  name: HyperDX Panel - Detect
  author: righettod
  severity: info
  description: |
    HyperDX panel was discovered.
  reference:
    - https://github.com/hyperdxio/hyperdx
    - https://www.hyperdx.io/
  metadata:
    max-request: 1
    verified: true
    shodan-query: html:"hyperdx"
  tags: panel,hyperdx,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}/search"

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains_any(to_lower(body), ">hyperdx</span>", ">loading hyperdx<")'
        condition: and

    extractors:
      - type: regex
        part: body
        group: 1
        regex:

          - '(?i)"version":"([0-9.]+)"'
# digest: 4b0a00483046022100f03832608b044a61368bdcefaf122ed90e097862fbabe8ba1f67509b3e53137702210085f0021c65343ff10afb6f43a399bcbb4406d6c556f881a79f3c38f7d97a3484:922c64590222798bb761d5b6d8e72950