#!/usr/bin/env python3
"""
NetScaler CVE-2025-6543 Vulnerable Simulator
Simulates a vulnerable NetScaler ADC/Gateway for testing Nuclei templates
"""

from flask import Flask, request, make_response, send_file
import ssl
import threading
import time
import io
import base64

app = Flask(__name__)

# Simulate NetScaler favicon (base64 encoded)
NETSCALER_FAVICON = """
iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz
AAAAN1wAADdcBQD2lJ8AAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwaAAAA
"""

# Vulnerable NetScaler version
VULNERABLE_VERSION = "13.1-51.15"

@app.route('/favicon.ico')
def favicon():
    """Serve NetScaler favicon with proper headers"""
    response = make_response()
    response.headers['Server'] = f'NetScaler-Web/{VULNERABLE_VERSION}'
    response.headers['Set-Cookie'] = 'NSC_AAAC=xyz123; path=/'
    response.headers['Content-Type'] = 'image/x-icon'

    # Return a simple favicon
    favicon_data = base64.b64decode(NETSCALER_FAVICON + "=" * (-len(NETSCALER_FAVICON) % 4))
    return favicon_data, 200, response.headers

@app.route('/logon/LogonPoint/index.html')
def logon_page():
    """Simulate NetScaler Gateway login page"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>NetScaler Gateway</title>
        <meta name="description" content="NetScaler Gateway Login">
    </head>
    <body>
        <div class="login-container">
            <h1>NetScaler Gateway</h1>
            <div class="version-info">NetScaler {VULNERABLE_VERSION}</div>
            <form action="/logon/LogonPoint/Authentication/Login" method="post">
                <input type="text" name="login" placeholder="Username">
                <input type="password" name="passwd" placeholder="Password">
                <input type="submit" value="Log On">
            </form>
        </div>
        <!-- NetScaler Gateway Version: {VULNERABLE_VERSION} -->
    </body>
    </html>
    """

    response = make_response(html_content)
    response.headers['Server'] = f'NetScaler-Web/{VULNERABLE_VERSION}'
    response.headers['Set-Cookie'] = f'NSC_TMAS=deadbeef; path=/'
    response.headers['Content-Type'] = 'text/html'
    return response

@app.route('/nitro/v1/config/nsversion')
def nitro_version():
    """Simulate NetScaler NITRO API version endpoint"""
    version_data = {
        "nsversion": {
            "version": VULNERABLE_VERSION,
            "builddate": "Dec 15 2023, 10:30:15",
            "builtby": "root",
            "edition": "Standard",
            "product": "NetScaler"
        }
    }

    response = make_response(str(version_data))
    response.headers['Server'] = f'NetScaler-Web/{VULNERABLE_VERSION}'
    response.headers['Content-Type'] = 'application/json'
    return response

@app.route('/')
def root():
    """Root endpoint with NetScaler headers"""
    response = make_response("NetScaler Gateway")
    response.headers['Server'] = f'NetScaler-Web/{VULNERABLE_VERSION}'
    response.headers['Set-Cookie'] = 'NSC_ROOT=abcd1234; path=/'
    return response

@app.errorhandler(404)
def not_found(error):
    """404 handler with NetScaler headers"""
    response = make_response("Not Found", 404)
    response.headers['Server'] = f'NetScaler-Web/{VULNERABLE_VERSION}'
    return response

def run_http_server():
    """Run HTTP server on port 80"""
    app.run(host='0.0.0.0', port=80, debug=False)

def run_https_server():
    """Run HTTPS server on port 443"""
    context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE

    # Create self-signed certificate for testing
    import tempfile
    import os

    cert_file = tempfile.NamedTemporaryFile(mode='w', suffix='.pem', delete=False)
    key_file = tempfile.NamedTemporaryFile(mode='w', suffix='.key', delete=False)

    # Simple self-signed cert (for testing only)
    cert_content = """-----BEGIN CERTIFICATE-----
MIICljCCAX4CCQDKg8N8N8N8NjANBgkqhkiG9w0BAQsFADCBjDELMAkGA1UEBhMC
VVMxCzAJBgNVBAgMAkNBMRYwFAYDVQQHDA1TYW4gRnJhbmNpc2NvMRMwEQYDVQQK
DApOZXRTY2FsZXIxEzARBgNVBAsMCk5ldFNjYWxlcjEuMCwGA1UEAwwlbmV0c2Nh
bGVyLXRlc3QuZXhhbXBsZS5jb20wHhcNMjMwMTAxMDAwMDAwWhcNMjQwMTAxMDAw
MDAwWjCBjDELMAkGA1UEBhMCVVMxCzAJBgNVBAgMAkNBMRYwFAYDVQQHDA1TYW4g
RnJhbmNpc2NvMRMwEQYDVQQKDApOZXRTY2FsZXIxEzARBgNVBAsMCk5ldFNjYWxl
cjEuMCwGA1UEAwwlbmV0c2NhbGVyLXRlc3QuZXhhbXBsZS5jb20wXDANBgkqhkiG
9w0BAQEFAAOCAQsAMIIBCgKCAQEA1234567890
-----END CERTIFICATE-----"""

    key_content = """-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDXNzc3Nzc3Nzc3
Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3
-----END PRIVATE KEY-----"""

    cert_file.write(cert_content)
    key_file.write(key_content)
    cert_file.close()
    key_file.close()

    try:
        app.run(host='0.0.0.0', port=443, debug=False, ssl_context='adhoc')
    except:
        # Fallback to simple HTTPS
        app.run(host='0.0.0.0', port=443, debug=False)

if __name__ == '__main__':
    print("🚀 Starting NetScaler Vulnerable Simulator")
    print(f"📋 Simulating NetScaler version: {VULNERABLE_VERSION}")
    print("🌐 HTTP Server: http://localhost:80")
    print("🔒 HTTPS Server: https://localhost:443")
    print("⚠️  This is a VULNERABLE simulator for testing purposes only!")

    # Start HTTP server in a separate thread
    http_thread = threading.Thread(target=run_http_server, daemon=True)
    http_thread.start()

    # Small delay to let HTTP server start
    time.sleep(1)

    # Start HTTPS server (this will block)
    try:
        run_https_server()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down NetScaler simulator...")
    except Exception as e:
        print(f"❌ Error starting HTTPS server: {e}")
        print("🔄 Running HTTP only...")
        http_thread.join()