# CVE-2025-7775 NetScaler ADC/Gateway Buffer Overflow PoC

## 🚨 CRITICAL VULNERABILITY - KEV Listed

**CVE-2025-7775** is a critical buffer overflow vulnerability in Citrix NetScaler ADC and NetScaler Gateway affecting IPv6 service configurations. This vulnerability has been added to CISA's Known Exploited Vulnerabilities (KEV) catalog.

### ⚠️ DISCLAIMER
This Proof of Concept (PoC) is provided for **EDUCATIONAL AND AUTHORIZED TESTING PURPOSES ONLY**. Only use this against systems you own or have explicit written permission to test. Unauthorized use is illegal and unethical.

## Vulnerability Details

- **CVE ID**: CVE-2025-7775
- **CVSS Score**: 9.8 (Critical)
- **Affected Products**: NetScaler ADC, NetScaler Gateway
- **Affected Versions**: 
  - NetScaler 13.1 (builds < 59.19)
  - NetScaler 14.1 (builds < 47.46)
  - NetScaler 13.1-FIPS (builds < 37.236)
  - NetScaler NDcPP (builds < 37.236)
- **Attack Vector**: Network (Remote)
- **Authentication**: Not required
- **Impact**: Remote Code Execution, Denial of Service

### Technical Details

The vulnerability exists in Gateway and LB virtual servers bound with IPv6 services. Memory overflow occurs when processing specially crafted requests to IPv6-enabled services, allowing attackers to:

- Execute arbitrary code remotely
- Cause denial of service
- Potentially gain administrative access

## Repository Structure

```
CVE-2025-7775-PoC/
├── Dockerfile                          # Docker container for simulation
├── docker-compose.yml                  # Easy deployment setup
├── apache-netscaler.conf              # Apache configuration
├── favicon.ico                        # NetScaler favicon
├── .github/workflows/test-poc.yml     # CI/CD testing
├── netscaler-sim/                     # Simulation files
│   ├── logon/LogonPoint/index.html   # Login page simulation
│   ├── nitro/v1/config/nsversion.php # Version API
│   └── api/v1/configuration.php      # Vulnerable endpoint
└── scripts/                          # Testing tools
    ├── exploit.py                    # Main exploitation script
    ├── log_scanner.py               # Log analysis tool
    └── start-services.sh            # Container startup script
```

## Quick Start

### Option 1: Docker (Recommended)

```bash
# Clone the repository
git clone https://github.com/your-org/CVE-2025-7775-PoC.git
cd CVE-2025-7775-PoC

# Build and run simulation environment
docker build -t cve-2025-7775-sim .
docker run -d -p 8080:80 -p 8443:443 --name netscaler-sim cve-2025-7775-sim

# Wait for services to start
sleep 10

# Test the simulation
curl http://localhost:8080/logon/LogonPoint/index.html
```

### Option 2: Docker Compose

```bash
# Start the complete environment
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

## Testing the Vulnerability

### 1. Run the Nuclei Template

```bash
# Install Nuclei (if not already installed)
go install -v github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest

# Test against simulation environment
nuclei -t ../http/cves/2025/CVE-2025-7775.yaml -target http://localhost:8080 -debug

# Test against real target (authorized testing only)
nuclei -t ../http/cves/2025/CVE-2025-7775.yaml -target https://your-netscaler.com
```

### 2. Run the Python Exploit Script

```bash
cd scripts

# Test against simulation
python3 exploit.py -t http://localhost:8080 -v

# Test against real target (authorized testing only)
python3 exploit.py -t https://your-netscaler.com --timeout 15
```

### 3. Analyze Logs

```bash
# Scan simulation logs
python3 log_scanner.py --log-file /var/log/netscaler-sim.log --format json

# Scan web server logs
python3 log_scanner.py --scan-dir /var/log/apache2 --output scan-report.txt
```

## Simulation Environment Features

### NetScaler Simulation
- **Authentic UI**: Realistic NetScaler Gateway login interface
- **API Endpoints**: NITRO API and configuration endpoints
- **Version Detection**: Simulates vulnerable NetScaler versions
- **IPv6 Services**: Simulates IPv6-enabled service configurations
- **Buffer Overflow**: Simulates memory overflow conditions

### Security Features
- **Safe Testing**: No actual exploitation, only simulation
- **Logging**: Comprehensive request/response logging
- **Monitoring**: Real-time log analysis and alerting
- **Isolation**: Containerized environment for safe testing

## Detection and Mitigation

### Detection Indicators

**Network Traffic:**
- Large POST requests to `/api/v1/configuration`
- IPv6-related configuration requests
- Unusual User-Agent strings containing "CVE-2025-7775"
- Requests with `X-NSCP-API` headers

**Log Patterns:**
```
BUFFER_OVERFLOW_DETECTED
Memory overflow in IPv6 service
Content-Length: [large values > 8192]
config_type=ipv6_service
```

**System Indicators:**
- NetScaler service crashes or restarts
- Memory corruption errors in logs
- Unusual network connections from NetScaler devices

### Mitigation Strategies

1. **Immediate Actions:**
   - Apply vendor security patches immediately
   - Monitor NetScaler devices for suspicious activity
   - Implement network segmentation around NetScaler devices

2. **Long-term Security:**
   - Regular security assessments of NetScaler configurations
   - Implement Web Application Firewall (WAF) rules
   - Monitor for CVE-2025-7775 exploitation attempts

3. **Patch Information:**
   - NetScaler 13.1: Upgrade to build 59.19 or later
   - NetScaler 14.1: Upgrade to build 47.46 or later
   - NetScaler 13.1-FIPS: Upgrade to build 37.236 or later

## Shodan/Fofa Queries

**Shodan:**
```
http.favicon.hash:-1292923998 OR http.favicon.hash:-1166125415
title:"Citrix Gateway" OR title:"NetScaler Gateway"
```

**Fofa:**
```
icon_hash="-1292923998" || icon_hash="-1166125415"
title="citrix gateway" || title="netscaler gateway"
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add your improvements
4. Test thoroughly
5. Submit a pull request

## Legal Notice

This tool is provided for educational and authorized security testing purposes only. Users are responsible for complying with all applicable laws and regulations. The authors assume no liability for misuse of this tool.

## References

- [CVE-2025-7775 - NVD](https://nvd.nist.gov/vuln/detail/CVE-2025-7775)
- [Citrix Security Bulletin](https://support.citrix.com/support-home/kbsearch/article?articleNumber=CTX694938)
- [CISA KEV Catalog](https://www.cisa.gov/known-exploited-vulnerabilities-catalog)
- [NetScaler Documentation](https://docs.netscaler.com/)

## License

MIT License - See LICENSE file for details.

---

**⚠️ Remember: Only test against systems you own or have explicit permission to test!**
