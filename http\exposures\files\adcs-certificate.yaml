id: adcs-certificate

info:
  name: Microsoft AD CS Web Enrollment - Detect
  author: pastaga,defte,atomiczsec
  severity: info
  description: |
    Detects Microsoft Active Directory Certificate Services (AD CS) Web Enrollment and certificate download pages.
  metadata:
    verified: true
    max-request: 3
    shodan-query: html:"/certenroll"
  tags: ad,adcs,exposure,files,web-enrollment,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}/certenroll/"
      - "{{BaseURL}}/CertEnroll/"
      - "{{BaseURL}}/certsrv/"
      - "{{BaseURL}}/certsrv"

    host-redirects: true
    max-redirects: 2
    stop-at-first-match: true

    matchers-condition: or
    matchers:
      - type: dsl
        name: certenroll
        dsl:
          - 'status_code == 200'
          - 'contains_any(body, ".crl", ".crt", ".cer", ".p7b")'
          - 'contains_any(tolower(body), "certenroll", "locmscertsrv", "certificate services")'
        condition: and

      - type: dsl
        name: certsrv
        dsl:
          - 'status_code == 200 || status_code == 401 || status_code == 403'
          - 'contains_any(tolower(body), "locmscertsrv", "certificate services", "web enrollment")'
        condition: and
# digest: 4b0a00483046022100a0491772103eabda138ce6d51e08bb0810723bb1da14bb2e3d6e33d40e9910cf0221009128a1399d033ee2dff8c0a7b86253e770679f38d2a2ae83e681eec519a68296:922c64590222798bb761d5b6d8e72950