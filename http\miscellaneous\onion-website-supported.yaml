id: onion-website-supported

info:
  name: Onion Website Supported via Onion-Location Header
  author: rxerium
  severity: info
  description: |
    Identified websites that supported Tor network access through the Onion-Location HTTP response header, which pointed to a corresponding .onion service for enhanced privacy and anonymity.
  metadata:
    verified: true
    max-request: 1
  tags: misc,osint,tor,onion

http:
  - method: GET
    path:
      - "{{BaseURL}}"

    matchers:
      - type: regex
        part: header
        regex:
          - '(?i)onion-location:\s*https?://[a-z0-9]+\.onion'

    extractors:
      - type: regex
        part: header
        group: 1
        regex:
          - '(?i)onion-location:\s*(https?://[a-z0-9]+\.onion)'
# digest: 4b0a00483046022100f21d9b3c6b939f3ce26d35cd9aada0b3a3c09678f72de1cbcaea48bf805154d2022100db504cc44bd84a8a29f86a30e50405eeb43da03159f1e7ecd523e8784ebe333c:922c64590222798bb761d5b6d8e72950