id: akuiteo-panel

info:
  name: <PERSON><PERSON><PERSON><PERSON> Login Panel - Detect
  author: righettod
  severity: info
  description: |
    Akuiteo products was detected.
  reference:
    - https://www.akuiteo.com/en/
  metadata:
    verified: true
    max-request: 1
    shodan-query: title:"Akuiteo"
  tags: panel,akuiteo,login,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}"
      - "{{BaseURL}}/akuiteo.collabs/login/login.html"
      - "{{BaseURL}}/akuiteo/login.html/"

    redirects: true
    max-redirects: 2

    stop-at-first-match: true
    matchers:
      - type: dsl
        dsl:
          - 'contains_any(to_lower(body), "akuiteo collabs</title>", "akuiteo mobile</title>", "akuiteo</title>", "<title>[akuiteo]")'
        condition: and

    extractors:
      - type: regex
        part: header
        group: 1
        regex:
          - '(?i)x-akuiteo-masterhash:\s+([0-9a-f]+)'
# digest: 4a0a00473045022100d6b0d3d1a4856dce255ea9f2b8037772d07344e767cc124574412f32910862c902202109a3282fc0569d7c7ca8853b6548540507401ecc25205e978636ab16bce5d8:922c64590222798bb761d5b6d8e72950