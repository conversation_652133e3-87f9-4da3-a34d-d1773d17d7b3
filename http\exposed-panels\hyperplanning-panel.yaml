id: hyperplanning-panel

info:
  name: HYPERPLANNING Login Panel - Detect
  author: righettod
  severity: info
  description: |
    HYPERPLANNING products was detected.
  reference:
    - https://www.index-education.com/fr/presentation-hyperplanning.php
  metadata:
    max-request: 1
    shodan-query: http.title:"HYPERPLANNING"
  tags: panel,hyperplanning,login,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}"

    host-redirects: true

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains_any(to_lower(body), "hyperplanning</title>", "content=\"hyperplanning\"")'
        condition: and

    extractors:
      - type: regex
        part: header
        group: 1
        regex:
          - '(?i)Server:\s+HYPERPLANNING\s+([0-9.\s\-]+)'
# digest: 4b0a00483046022100e91c1e06b104263be46fe8ead461ebd195e396e3ce8d0161dd1dbfd5242ff8c4022100d8bba18c12fbed8937b1d5338b9d0111ba921a305650869c0580ba783ae7fb73:922c64590222798bb761d5b6d8e72950