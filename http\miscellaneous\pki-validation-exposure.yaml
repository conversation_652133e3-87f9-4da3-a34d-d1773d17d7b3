id: pki-validation-exposure

info:
  name: Well-Known PKI Validation Directory
  author: rxerium
  severity: info
  description: |
    Detects CA/B Forum PKI validation artefacts directory.
  impact: |
    Presence of this well-known resource can expose implementation details or policies.
  reference:
    - https://www.namecheap.com/support/knowledgebase/article.aspx/10025/68/how-to-complete-httpbased-validation-upload-a-validation-file-option/
  metadata:
    verified: true
    max-request: 1
    google-query: inurl:"/.well-known/pki-validation/"
  tags: well-known,pki,certificate,validation,miscellaneous,misc

http:
  - method: GET
    path:
      - "{{BaseURL}}/.well-known/pki-validation/"
      - "{{BaseURL}}/well-known/pki-validation/"

    redirects: true
    stop-at-first-match: true
    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "pki-validation"
          - "Parent Directory"
        condition: and

      - type: status
        status:
          - 200
# digest: 4a0a004730450220670ecdab87e8f35fe567b14e278462af341b81c987805dac8d2746d0be6b9bbc02210083d2e7366fcf1da4b5ff40639eb9f15862a0563a890fb61669d0c4ed7752ca0c:922c64590222798bb761d5b6d8e72950