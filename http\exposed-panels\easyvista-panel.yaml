id: easyvista-panel

info:
  name: EasyVista Login Panel - Detect
  author: righettod
  severity: info
  description: |
    EasyVista login panel was detected.
  reference:
    - https://www.easyvista.com/
  metadata:
    verified: true
    max-request: 1
    shodan-query: http.title:"Easyvista"
  tags: panel,easyvista,login,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}/index.php"

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains_any(to_lower(body), "<title>easyvista apps</title>", "easyvista-bundle.min.js", "packages_com_easyvista_core")'
        condition: and

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - '(?i)package:\s+"([a-z0-9._-]+)"'
          - '(?i)version&nbsp;:&nbsp;([a-z0-9._-]+)'
# digest: 490a00463044022069d25f2116b903e3b2827d7d693deb900a744ba64d2f1796626cf5609ac5c7710220198b261857c97bb042dd26a72593fea9f8374c2f4aeadf0e3a50b2a33ab89069:922c64590222798bb761d5b6d8e72950