@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================================================
echo                NetScaler CVE-2025-6543 Complete Test Automation
echo ========================================================================
echo.
echo This script will:
echo 1. Check prerequisites (Python, Nuclei, Flask)
echo 2. Start a vulnerable NetScaler simulator
echo 3. Run Nuclei scan with full debug output
echo 4. Clean up automatically
echo.

REM Check Python
echo [1/6] Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo Please install Python 3.x and try again
    pause
    exit /b 1
)
echo ✅ Python found

REM Check Nuclei
echo [2/6] Checking Nuclei...
nuclei -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Nuclei is not installed or not in PATH
    echo Please install Nuclei and try again
    pause
    exit /b 1
)
echo ✅ Nuclei found

REM Install Flask
echo [3/6] Installing Flask...
python -m pip install flask --quiet
if %errorlevel% neq 0 (
    echo ❌ ERROR: Failed to install Flask
    pause
    exit /b 1
)
echo ✅ Flask ready

REM Check template exists
echo [4/6] Checking template...
if not exist "http\cves\2025\CVE-2025-6543.yaml" (
    echo ❌ ERROR: Template not found: http\cves\2025\CVE-2025-6543.yaml
    echo Make sure you're in the nuclei-templates directory
    pause
    exit /b 1
)
echo ✅ Template found

REM Start NetScaler simulator
echo [5/6] Starting NetScaler simulator...
echo Starting vulnerable NetScaler simulator on port 80...
start /B python netscaler-simulator.py
timeout /t 3 /nobreak >nul
echo ✅ Simulator started

REM Test connectivity
echo Testing connectivity...
powershell -Command "try { Invoke-WebRequest -Uri 'http://127.0.0.1' -Method Head -TimeoutSec 5 | Out-Null; Write-Host '✅ Connectivity confirmed' } catch { Write-Host '❌ Connection failed'; exit 1 }"
if %errorlevel% neq 0 (
    echo ❌ ERROR: Cannot connect to simulator
    taskkill /F /IM python.exe >nul 2>&1
    pause
    exit /b 1
)

echo.
echo ========================================================================
echo                           NUCLEI SCAN RESULTS
echo ========================================================================
echo.

REM Run Nuclei scan
echo [6/6] Running Nuclei scan with debug output...
nuclei -t http/cves/2025/CVE-2025-6543.yaml -u http://127.0.0.1 -debug -v

echo.
echo ========================================================================
echo                              CLEANUP
echo ========================================================================
echo.

REM Cleanup
echo Stopping NetScaler simulator...
taskkill /F /IM python.exe >nul 2>&1
echo ✅ Simulator stopped

echo.
echo ========================================================================
echo                         AUTOMATION COMPLETE
echo ========================================================================
echo.
echo ✅ NetScaler CVE-2025-6543 testing completed successfully!
echo.
echo Summary:
echo - Template: http/cves/2025/CVE-2025-6543.yaml
echo - Target: NetScaler Simulator (Python Flask)
echo - Version: 13.1-51.15 (Vulnerable)
echo - Simulator: Cleaned up
echo.
echo Press any key to exit...
pause >nul
