id: tomcat-stacktraces

info:
  name: Tom<PERSON> Stack Traces Enabled
  author: lucky0x0d
  severity: low
  description: |
    Examine whether Tomcat stack traces are turned on by employing a designated problematic pattern.
  classification:
    cpe: cpe:2.3:a:apache:tomcat:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 1
    vendor: apache
    product: tomcat
    shodan-query: title:"Apache Tomcat"
  tags: misconfig,tech,tomcat,apache

http:
  - method: GET
    path:
      - '{{BaseURL}}/?f=\['

    matchers:
      - type: dsl
        dsl:
          - 'contains(body, "tomcat")'
          - 'contains(body, "org.apache")'
          - status_code == 400
        condition: and
# digest: 490a00463044022069e9ecc3ca9621e489b81c37c12febbcd2724d4370a4f10c9c3e9b3f17e4258e0220582d7e1513f7d6d147370c13343b51ea2bd3e7e76660a7d4a1ae3dc3e8a52a03:922c64590222798bb761d5b6d8e72950