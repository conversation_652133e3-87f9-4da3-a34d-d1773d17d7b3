<VirtualHost *:80>
    <PERSON><PERSON><PERSON><PERSON> webmaster@localhost
    DocumentRoot /var/www/html

    # Enable rewrite engine for path normalization simulation
    RewriteEngine On
    
    # Simulate the path normalization vulnerability that allows ..;/ traversal
    # This mimics the Apache configuration found in Mitel MiCollab
    RewriteRule ^/npm-pwg/\.\.;/(.*)$ /$1 [L,QSA]
    
    # Standard npm-pwg redirects (simulating Mitel's config)
    RewriteRule ^/npm-pwg$ /npm-pwg/loginForm.jsp [R]
    RewriteRule ^/npm-pwg/(.*)$ /npm-pwg/$1 [L,QSA]
    
    # Proxy configuration to simulate the vulnerable setup
    ProxyPreserveHost On
    ProxyPass /npm-admin/ http://localhost:80/npm-admin/
    ProxyPassReverse /npm-admin/ http://localhost:80/npm-admin/
    
    # Enable PHP processing
    <FilesMatch \.php$>
        SetHandler application/x-httpd-php
    </FilesMatch>
    
    # Directory permissions
    <Directory /var/www/html>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    # Custom error and access logs
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
    
    # Security headers (but still vulnerable to the CVE)
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</VirtualHost>
