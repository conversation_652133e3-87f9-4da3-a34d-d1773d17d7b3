#!/bin/bash

# CVE-2025-7775 NetScaler Simulation Environment Startup Script
# This script starts all necessary services for the simulation environment

set -e

echo "Starting CVE-2025-7775 NetScaler Simulation Environment..."

# Create necessary directories
mkdir -p /var/log
mkdir -p /var/tmp
mkdir -p /var/www/netscaler/var/tmp

# Set proper permissions
chown -R www-data:www-data /var/www/netscaler
chmod -R 755 /var/www/netscaler
chmod 777 /var/tmp
chmod 777 /var/www/netscaler/var/tmp

# Create log files
touch /var/log/netscaler-sim.log
chown www-data:www-data /var/log/netscaler-sim.log

# Enable PHP modules
phpenmod curl json

# Start Apache in foreground
echo "Starting Apache web server..."
apache2ctl -D FOREGROUND &

# Wait for Apache to start
sleep 2

# Verify services are running
if pgrep apache2 > /dev/null; then
    echo "✓ Apache web server started successfully"
else
    echo "✗ Failed to start Apache web server"
    exit 1
fi

# Display service information
echo ""
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              CVE-2025-7775 Simulation Environment           ║"
echo "╠══════════════════════════════════════════════════════════════╣"
echo "║ HTTP Service:  http://localhost:80                          ║"
echo "║ HTTPS Service: https://localhost:443                        ║"
echo "║                                                              ║"
echo "║ NetScaler Login: /logon/LogonPoint/index.html               ║"
echo "║ NITRO API:       /nitro/v1/config/nsversion                 ║"
echo "║ Config API:      /api/v1/configuration                      ║"
echo "║                                                              ║"
echo "║ Logs: /var/log/netscaler-sim.log                           ║"
echo "║                                                              ║"
echo "║ ⚠️  SIMULATION ENVIRONMENT - FOR TESTING ONLY ⚠️            ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

# Monitor logs in background
tail -f /var/log/netscaler-sim.log &

# Keep container running
wait
