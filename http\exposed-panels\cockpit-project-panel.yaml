id: cockpit-project-panel

info:
  name: Cockpit Project Login Panel - Detect
  author: righettod
  severity: info
  description: |
    Cockpit Project products was detected.
  reference:
    - https://github.com/cockpit-project/cockpit
    - https://cockpit-project.org/
  metadata:
    max-request: 1
    verified: true
    shodan-query: http.html:"cockpit/static/login.css"
  tags: panel,cockpit,login

http:
  - method: GET
    path:
      - "{{BaseURL}}/"

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains_all(to_lower(body), "cockpit/", "is_cockpit_client")'
        condition: and

    extractors:
      - type: regex
        part: body
        name: os
        group: 1
        regex:
          - '(?i)"PRETTY_NAME"\s*:\s*"(.*?)"'
        internal: true

      - type: regex
        part: body
        name: hostname
        group: 1
        regex:
          - '(?i)"hostname"\s*:\s*"(.*?)"'
        internal: true

      - type: dsl
        dsl:
          - '"OS: " + os + " | Hostname: " + hostname'
# digest: 4b0a004830460221008a1092fc16f9aade1f235ed5a648fc61647c04884d0219feff6cba29077eac85022100c2e7a4a58c5b81d85e61ad848573210cc0ade6cf3d5ed67702a58bd768ced31c:922c64590222798bb761d5b6d8e72950