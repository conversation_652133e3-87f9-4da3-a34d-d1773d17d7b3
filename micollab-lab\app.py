from flask import Flask, request, Response
import time

app = Flask(__name__)

# Simulated vulnerable endpoint
@app.route("/np-app/api/d-vmail/get-msg-status", methods=["POST"])
def get_msg_status():
    # Expect: Content-Type: application/x-www-form-urlencoded
    body = request.get_data(as_text=True) or ""
    print(f"[DEBUG] Received POST body: {body}")
    
    # If your PoC payload includes SLEEP(6), emulate time-based SQLi:
    if "SLEEP(6)" in body.upper():
        print("[DEBUG] SLEEP(6) detected - introducing 6 second delay")
        time.sleep(6)
        # Many real systems still return 200; adjust if your template expects 200.
        return Response("OK (delayed)", status=200, mimetype="text/plain")
    
    # Non-matching requests return quickly
    print("[DEBUG] No SLEEP detected - returning immediately")
    return Response("OK", status=200, mimetype="text/plain")

@app.route("/")
def root():
    return "Micollab mock alive", 200

if __name__ == "__main__":
    print("[INFO] Starting Mitel MiCollab mock server on 0.0.0.0:8080")
    app.run(host="0.0.0.0", port=8080)
