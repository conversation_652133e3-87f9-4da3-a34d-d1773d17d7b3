from flask import Flask, request, Response
import time

app = Flask(__name__)

# Remove Flask's default server header and add Mitel-like headers
@app.after_request
def add_mitel_headers(response):
    # Remove Flask/Werkzeug server header
    response.headers.pop('Server', None)
    # Add realistic Mitel MiCollab server headers
    response.headers['Server'] = 'Apache-Coyote/1.1'
    response.headers['X-Powered-By'] = 'Servlet/3.0'
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    return response

# Simulated vulnerable endpoint - matches real Mitel MiCollab behavior
@app.route("/np-app/api/d-vmail/get-msg-status", methods=["POST"])
def get_msg_status():
    # Expect: Content-Type: application/x-www-form-urlencoded
    body = request.get_data(as_text=True) or ""
    print(f"[DEBUG] Received POST body: {body}")

    # If your PoC payload includes SLEEP(6), emulate time-based SQLi:
    if "SLEEP(6)" in body.upper():
        print("[DEBUG] SLEEP(6) detected - introducing 6 second delay")
        time.sleep(6)
        # Return JSON response like real Mitel system
        return Response('{"status":"success","message":"Message status retrieved"}',
                       status=200, mimetype="application/json")

    # Non-matching requests return quickly with realistic response
    print("[DEBUG] No SLEEP detected - returning immediately")
    return Response('{"status":"success","message":"Message status retrieved"}',
                   status=200, mimetype="application/json")

@app.route("/")
def root():
    return Response("""<!DOCTYPE html>
<html>
<head><title>MiCollab Web Client</title></head>
<body>
<h1>Mitel MiCollab</h1>
<p>Version 9.8.0.33</p>
<p>NuPoint Unified Messaging</p>
</body>
</html>""", status=200, mimetype="text/html")

# Add some realistic Mitel endpoints
@app.route("/portal/", methods=["GET"])
def portal():
    return Response("MiCollab Portal", status=200, mimetype="text/html")

@app.route("/npm-pwg/loginForm.jsp", methods=["GET"])
def npm_login():
    return Response("NPM Login Form", status=200, mimetype="text/html")

if __name__ == "__main__":
    print("[INFO] Starting Mitel MiCollab mock server (v9.8.0.33) on 0.0.0.0:8080")
    app.run(host="0.0.0.0", port=8080)
