id: sql-server-dump

info:
  name: SQL Server - Dump Files
  author: userdehghani
  severity: medium
  description: |
    A SQL Server dump file was found
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
    cvss-score: 5.3
    cwe-id: CWE-200
  metadata:
    max-request: 21
  tags: exposure,backup,sql-server

http:
  - method: GET
    path:
      - "{{BaseURL}}{{paths}}"

    payloads:
      paths:
        - "/sa.bak"
        - "/wwwroot.bak"
        - "/backup.bak"
        - "/database.bak"
        - "/data.bak"
        - "/db_backup.bak"
        - "/dbdump.bak"
        - "/db.bak"
        - "/dump.bak"
        - "/{{Hostname}}.bak"
        - "/{{Hostname}}_db.bak"
        - "/localhost.bak"
        - "/mysqldump.bak"
        - "/mysql.bak"
        - "/site.bak"
        - "/sql.bak"
        - "/temp.bak"
        - "/translate.bak"
        - "/users.bak"
        - "/www.bak"
        - "/wp-content/uploads/dump.bak"
        - "/wp-content/mysql.bak"

    headers:
      Range: "bytes=0-500"
    max-size: 500 # Size in bytes - Max Size to read from server response

    matchers-condition: and
    matchers:
      - type: binary
        part: body
        binary:
          - "54415045" # Microsoft Tape Format

      - type: status
        status:
          - 200
# digest: 4a0a00473045022100c67b9dfbc647af910b68b657aee2293338c679049b5e66d62f1129166b3d50b502207dc7133204290bd8a5436ae78513a1ed9c7e278769cc7a5dc150de124dc8ab3a:922c64590222798bb761d5b6d8e72950