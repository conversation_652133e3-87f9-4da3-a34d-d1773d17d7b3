id: hangfire-dashboard

info:
  name: Hangfire Dashboard Panel - Detect
  author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,righettod
  severity: info
  description: Hangfire Dashboard panel was detected.
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N
    cwe-id: CWE-200
    cpe: cpe:2.3:a:hangfire:hangfire:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 2
    vendor: hangfire
    product: hangfire
    shodan-query:
      - title:"Overview – Hangfire Dashboard"
      - http.title:"overview – hangfire dashboard"
    fofa-query: title="overview – hangfire dashboard"
    google-query: intitle:"overview – hangfire dashboard"
  tags: panel,hangfire

http:
  - method: GET
    path:
      - "{{BaseURL}}"
      - "{{BaseURL}}/hangfire"

    stop-at-first-match: true

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - 'Hangfire Dashboard'
          - 'Realtime Graph'
        condition: and
        case-insensitive: true

      - type: status
        status:
          - 200

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - 'Hangfire\s+([0-9.]+)'
# digest: 4a0a004730450220188936b65457aea1af1a075691be7274bad551bfa8a9922920c67c94c4a308a1022100c66bc001ead4416f9809eedb0e2d2c0369f48f6db2cd4432271263c382d005c7:922c64590222798bb761d5b6d8e72950