id: assetlinks-detect

info:
  name: Android Asset Links Configuration - Detect
  author: rxerium
  severity: info
  description: |
    The .well-known/assetlinks.json file was found on the target server. This file is used by Android applications to establish verified app-to-web domain associations through the Digital Asset Links protocol.
  reference:
    - https://developer.android.com/training/app-links/verify-android-applinks
  metadata:
    verified: true
    max-request: 1
    shodan-query: html:"assetlinks.json"
  tags: misc,assetlinks,compliance,assetlinks

http:
  - method: GET
    path:
      - "{{BaseURL}}/.well-known/assetlinks.json"

    matchers:
      - type: dsl
        dsl:
          - "status_code == 200"
          - "contains(body, 'android_app')"
          - "contains(content_type, 'application/json')"
        condition: and
# digest: 4a0a00473045022100d81b788d1693d5ac22849abb9a95ebcb84927e49da862f7093ae7c9811d6065f0220522698b58fbfd89f56ada707d9159ae3e22fdb0f80c8f320f788528060106605:922c64590222798bb761d5b6d8e72950