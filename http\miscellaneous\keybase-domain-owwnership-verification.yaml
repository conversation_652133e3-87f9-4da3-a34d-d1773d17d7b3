id: keybase-domain-owwnership-verification

info:
  name: Keybase Domain Ownership Verification
  author: rxerium
  severity: info
  description: |
    Detects presence of keybase.txt used to prove domain ownership via Keybase identity.
  reference:
    - https://book.keybase.io/account#proofs
  metadata:
    verified: true
    max-request: 1
    shodan-query: html:"keybase.txt"
  tags: keybase,identity,well-known,pgp,discovery,osint,misc

http:
  - method: GET
    path:
      - "{{BaseURL}}/.well-known/keybase.txt"

    matchers:
      - type: dsl
        dsl:
          - "status_code == 200 && contains(body, 'https://keybase.io/')"

    extractors:
      - type: regex
        name: keybase-username
        part: body
        regex:
          - "([a-zA-Z0-9_-]+) \\(https://keybase.io/[a-zA-Z0-9_-]+\\)"

      - type: regex
        name: pgp-message
        part: body
        regex:
          - "-----BEGIN PGP MESSAGE-----[\\s\\S]+?-----<PERSON><PERSON> PGP MESSAGE-----"
# digest: 4a0a00473045022100d38d032ad979e370d52ad4ddb21c6ad1f83e99d8b757d0cc041f17832895574e02200c29bbce3801096c3ef878ca8c5027cecefd7c658b1faa668a96abac9bcf2250:922c64590222798bb761d5b6d8e72950