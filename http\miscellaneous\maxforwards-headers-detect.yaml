id: maxforwards-headers-detect

info:
  name: Max-Forwards Header - Detect
  author: righettod
  severity: info
  description: Max-Forwards response header is specified.
  reference:
    - https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Max-Forwards
    - https://http.dev/max-forwards
    - https://twitter.com/irsdl/status/1337299267652825088
  metadata:
    verified: true
    max-request: 1
    shodan-query: "Max-Forwards:"
    fofa-query: header="max-forwards"
  tags: miscellaneous,misc,max-forwards

http:
  - method: GET
    path:
      - "{{BaseURL}}"

    matchers:
      - type: dsl
        dsl:
          - 'contains(to_lower(header), "max-forwards:")'

    extractors:
      - type: regex
        part: header
        regex:
          - '(?i)max-forwards:\s+([0-9]+)'
# digest: 4a0a0047304502203f3666b5911a18d4cb7e0eee2fadbda837a7dab1f6c58aaec43305b05eecf332022100f8ba467e46d73b25f97bd671bbdac7ee1e7e155767e0ac77a51d397d2ce4d386:922c64590222798bb761d5b6d8e72950