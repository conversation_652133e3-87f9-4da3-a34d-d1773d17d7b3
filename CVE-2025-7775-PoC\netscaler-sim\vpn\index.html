<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NetScaler Gateway</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .gateway-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 20px;
        }
        .version-badge {
            background: #ff6b6b;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            margin-left: 10px;
        }
        .service-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .service-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #f9f9f9;
        }
        .service-card h3 {
            margin-top: 0;
            color: #333;
        }
        .status-enabled {
            color: #28a745;
            font-weight: bold;
        }
        .status-vulnerable {
            color: #dc3545;
            font-weight: bold;
        }
        .ipv6-indicator {
            background: #ffc107;
            color: #000;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="gateway-container">
        <div class="header">
            <h1>NetScaler Gateway <span class="version-badge">VULNERABLE</span></h1>
            <p>Version: 13.1-58.15 | Build: 13.1-58.15.nc</p>
            <p>IPv6 Services: <span class="status-enabled">ENABLED</span> | 
               CVE-2025-7775: <span class="status-vulnerable">VULNERABLE</span></p>
        </div>

        <div class="service-list">
            <div class="service-card">
                <h3>Gateway Virtual Server <span class="ipv6-indicator">IPv6</span></h3>
                <p><strong>Status:</strong> <span class="status-enabled">Running</span></p>
                <p><strong>Service Type:</strong> HTTP6, SSL6</p>
                <p><strong>Address:</strong> ::1:443</p>
                <p><strong>Configuration:</strong> VPN, ICA Proxy, CVPN, RDP Proxy</p>
                <p><strong>Vulnerability:</strong> <span class="status-vulnerable">Buffer overflow in IPv6 service handling</span></p>
            </div>

            <div class="service-card">
                <h3>Load Balancer Virtual Server <span class="ipv6-indicator">IPv6</span></h3>
                <p><strong>Status:</strong> <span class="status-enabled">Running</span></p>
                <p><strong>Service Type:</strong> HTTP6</p>
                <p><strong>Address:</strong> ::1:80</p>
                <p><strong>Backend Services:</strong> 3 IPv6 services</p>
                <p><strong>Vulnerability:</strong> <span class="status-vulnerable">Memory overflow in LB IPv6 processing</span></p>
            </div>

            <div class="service-card">
                <h3>AAA Virtual Server</h3>
                <p><strong>Status:</strong> <span class="status-enabled">Running</span></p>
                <p><strong>Service Type:</strong> SSL</p>
                <p><strong>Address:</strong> *************:443</p>
                <p><strong>Authentication:</strong> LDAP, RADIUS</p>
                <p><strong>Vulnerability:</strong> <span class="status-vulnerable">Affected by IPv6 buffer overflow</span></p>
            </div>

            <div class="service-card">
                <h3>Management Interface</h3>
                <p><strong>Status:</strong> <span class="status-enabled">Running</span></p>
                <p><strong>NITRO API:</strong> Enabled</p>
                <p><strong>SNMP:</strong> v2c, v3</p>
                <p><strong>SSH:</strong> Enabled</p>
                <p><strong>Vulnerability:</strong> <span class="status-vulnerable">API endpoints vulnerable to buffer overflow</span></p>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px;">
            <h4 style="color: #856404; margin-top: 0;">⚠️ Security Alert - CVE-2025-7775</h4>
            <p style="color: #856404; margin-bottom: 0;">
                This NetScaler instance is vulnerable to CVE-2025-7775 buffer overflow. 
                IPv6 services are enabled and susceptible to memory overflow attacks that can lead to 
                remote code execution or denial of service. Immediate patching is recommended.
            </p>
        </div>

        <div style="margin-top: 20px; text-align: center; font-size: 12px; color: #666;">
            <p>NetScaler Gateway Simulation Environment | CVE-2025-7775 Testing</p>
            <p>⚠️ FOR EDUCATIONAL AND AUTHORIZED TESTING PURPOSES ONLY ⚠️</p>
        </div>
    </div>

    <script>
        // Simulate NetScaler behavior
        document.cookie = "NSC_AAAC=xyz123; path=/; secure";
        document.cookie = "NSC_TMAS=simulation; path=/";
        
        // Log access for testing
        console.log('NetScaler Gateway VPN interface accessed');
        console.log('IPv6 services detected: HTTP6, SSL6');
        console.log('CVE-2025-7775 vulnerability status: VULNERABLE');
    </script>
</body>
</html>
