id: bynder-panel

info:
  name: <PERSON><PERSON>gin Panel - Detect
  author: righettod
  severity: info
  description: |
    Bynder login panel was detected.
  reference:
    - https://www.bynder.com/en/
  metadata:
    verified: true
    max-request: 1
    shodan-query: http.favicon.hash:1017650009
  tags: panel,bynder,login,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}/login/"

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains_any(to_lower(body), "bynder.", "bynder brand portal", "bynder login")'
        condition: and

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - 'http-equiv="version"\s+content="([0-9\.]+)"'
# digest: 490a004630440220603edf7acf46f1f758df1a8e02dcff9398da6f5ec5802726dd8f48664623c630022013b41119b40225d46d5691786fefe6e69de267c912c1444b4c09267d4960d951:922c64590222798bb761d5b6d8e72950