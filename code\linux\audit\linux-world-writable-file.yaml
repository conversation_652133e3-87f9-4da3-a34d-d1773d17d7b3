id: linux-world-writable-file

info:
  name: Linux World-Writable File Permission
  author: songyaeji
  severity: high
  description: |
    System files were configured with world-writable (chmod o+w) permissions.Malicious users could modify them, leading to privilege escalation, backdoors, or service disruption.
  reference:
    - https://isms.kisa.or.kr
  tags: linux,local,audit,compliance,kisa

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      find / -type f -perm -0002 ! -path "/tmp/*" -exec ls -l {} \; 2>/dev/null

    matchers:
      - type: regex
        name: world-writable-files
        part: response
        regex:
          - "^-........w.*"
# digest: 4b0a00483046022100cb4eeed99539ca94bafeaaa60912f67fcac73a5d2c5a4f77d1f5085271959995022100e8df18d9e16d1dd7a187b6bde457f4706e1863500ba810dae15f383f8942bace:922c64590222798bb761d5b6d8e72950