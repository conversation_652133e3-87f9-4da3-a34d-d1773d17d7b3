version: '3.8'

services:
  netscaler-sim:
    build: .
    container_name: cve-2025-7775-netscaler-sim
    ports:
      - "8080:80"
      - "8443:443"
    environment:
      - APACHE_RUN_USER=www-data
      - APACHE_RUN_GROUP=www-data
      - APACHE_LOG_DIR=/var/log/apache2
    volumes:
      - ./logs:/var/log/apache2
      - ./netscaler-sim:/var/www/netscaler
    networks:
      - cve-2025-7775-net
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/logon/LogonPoint/index.html"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  log-analyzer:
    build: .
    container_name: cve-2025-7775-log-analyzer
    command: ["python3", "/opt/scripts/log_scanner.py", "--scan-dir", "/var/log", "--format", "json"]
    volumes:
      - ./logs:/var/log/apache2:ro
    networks:
      - cve-2025-7775-net
    depends_on:
      - netscaler-sim
    profiles:
      - analysis

  nuclei-scanner:
    image: projectdiscovery/nuclei:latest
    container_name: cve-2025-7775-nuclei
    command: ["-t", "/templates/CVE-2025-7775.yaml", "-target", "http://netscaler-sim", "-debug"]
    volumes:
      - ../http/cves/2025:/templates:ro
    networks:
      - cve-2025-7775-net
    depends_on:
      - netscaler-sim
    profiles:
      - testing

networks:
  cve-2025-7775-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  logs:
    driver: local
