<VirtualHost *:80>
    ServerName netscaler.local
    DocumentRoot /var/www/netscaler
    
    # NetScaler simulation headers
    Header always set Server "NetScaler-Web/1.0"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    
    # Enable PHP processing
    <FilesMatch \.php$>
        SetHandler application/x-httpd-php
    </FilesMatch>
    
    # NetScaler-style directory structure
    <Directory "/var/www/netscaler">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Simulate NetScaler authentication bypass for testing
        <Files "*.php">
            Header always set Set-Cookie "NSC_AAAC=xyz123; path=/; secure"
            Header always set Set-Cookie "NSC_TMAS=simulation; path=/"
        </Files>
    </Directory>
    
    # API endpoints
    Alias /api/v1 /var/www/netscaler/api/v1
    Alias /nitro/v1 /var/www/netscaler/nitro/v1
    
    # Logging for analysis
    ErrorLog ${APACHE_LOG_DIR}/netscaler_error.log
    CustomLog ${APACHE_LOG_DIR}/netscaler_access.log combined
    
    # Rewrite rules to simulate NetScaler behavior
    RewriteEngine On
    
    # Redirect root to login page
    RewriteRule ^/?$ /logon/LogonPoint/index.html [R=302,L]
    
    # Handle NITRO API calls
    RewriteRule ^/nitro/v1/config/nsversion$ /nitro/v1/config/nsversion.php [L]
    
    # Handle configuration API
    RewriteRule ^/api/v1/configuration$ /api/v1/configuration.php [L]
</VirtualHost>

<VirtualHost *:443>
    ServerName netscaler.local
    DocumentRoot /var/www/netscaler
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/netscaler.crt
    SSLCertificateKeyFile /etc/ssl/private/netscaler.key
    
    # NetScaler simulation headers
    Header always set Server "NetScaler-Web/1.0"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # Enable PHP processing
    <FilesMatch \.php$>
        SetHandler application/x-httpd-php
    </FilesMatch>
    
    # NetScaler-style directory structure
    <Directory "/var/www/netscaler">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Simulate NetScaler authentication bypass for testing
        <Files "*.php">
            Header always set Set-Cookie "NSC_AAAC=xyz123; path=/; secure"
            Header always set Set-Cookie "NSC_TMAS=simulation; path=/"
        </Files>
    </Directory>
    
    # API endpoints
    Alias /api/v1 /var/www/netscaler/api/v1
    Alias /nitro/v1 /var/www/netscaler/nitro/v1
    
    # Logging for analysis
    ErrorLog ${APACHE_LOG_DIR}/netscaler_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/netscaler_ssl_access.log combined
    
    # Rewrite rules to simulate NetScaler behavior
    RewriteEngine On
    
    # Redirect root to login page
    RewriteRule ^/?$ /logon/LogonPoint/index.html [R=302,L]
    
    # Handle NITRO API calls
    RewriteRule ^/nitro/v1/config/nsversion$ /nitro/v1/config/nsversion.php [L]
    
    # Handle configuration API
    RewriteRule ^/api/v1/configuration$ /api/v1/configuration.php [L]
</VirtualHost>
