id: addeventlistener-detect

info:
  name: Add DOM EventListener - Detection
  author: yavolo,dwisiswant0
  severity: info
  description: |
    Identifies the use of JavaScript addEventListener calls in the DOM.
  reference:
    - https://portswigger.net/web-security/dom-based/controlling-the-web-message-source
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:N
    cvss-score: 7.2
    cwe-id: CWE-79
  metadata:
    max-request: 1
  tags: miscellaneous,xss,misc

http:
  - method: GET
    path:
      - "{{BaseURL}}"

    matchers:
      - type: regex
        part: body
        regex:
          - (([\w\_]+)\.)?add[Ee]vent[Ll]istener\(["']?[\w\_]+["']? # Test cases: https://www.regextester.com/?fam=121118
# digest: 4b0a00483046022100807422492a0476784a4b277bf5c527d7d39c0f643a5a103e356793bc62985e450221009ac36d4168e18956589838ed60e25c1b4653fb35b00b96c82ab68d14d00e4bb4:922c64590222798bb761d5b6d8e72950