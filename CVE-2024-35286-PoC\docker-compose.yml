version: '3.8'

services:
  mitel-micollab-mock:
    build: .
    container_name: cve-2024-35286-mock
    ports:
      - "8080:80"
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=micollab
      - MYSQL_USER=micollab_user
      - MYSQL_PASSWORD=micollab_pass
    volumes:
      - ./logs:/var/log/apache2
    networks:
      - micollab-network

  nuclei-scanner:
    image: projectdiscovery/nuclei:latest
    container_name: nuclei-cve-2024-35286
    depends_on:
      - mitel-micollab-mock
    volumes:
      - ./nuclei-templates:/nuclei-templates
      - ./scan-results:/scan-results
    networks:
      - micollab-network
    command: >
      sh -c "
        sleep 10 &&
        nuclei -t /nuclei-templates/CVE-2024-35286.yaml 
        -target http://mitel-micollab-mock 
        -o /scan-results/cve-2024-35286-results.txt
        -v
      "

networks:
  micollab-network:
    driver: bridge

volumes:
  mysql-data:
