id: nostr-json-exposure

info:
  name: Well-Known Nostr JSON
  author: rxerium
  severity: info
  description: |
    Detects Nostr public key discovery JSON.
  impact: |
    Presence of this well-known resource can expose implementation details or policies.
  reference:
    - https://github.com/nostr-protocol/nips/blob/master/05.md
  metadata:
    google-query: inurl:"/.well-known/nostr.json"
  tags: well-known,nostr,miscellaneous,misc

http:
  - method: GET
    path:
      - "{{BaseURL}}/.well-known/nostr.json"

    max-redirects: 1
    redirects: true

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"names": {'

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200
# digest: 4b0a00483046022100d67d5f83c3ad469c5dee347b0975b48c8572ecbd567d5a1ef007b3d068b1c8aa022100ed9e15a782329c8083c158e9b3c24034558c6ad8a65ac5d11f8c809e3e30ff04:922c64590222798bb761d5b6d8e72950