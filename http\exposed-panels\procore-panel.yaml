id: procore-panel

info:
  name: Procore Login - Panel
  author: rxerium
  severity: info
  metadata:
    max-request: 2
    verified: true
    shodan-query: http.favicon.hash:**********
  tags: panel,login,detect,procore

http:
  - method: GET
    path:
      - "{{BaseURL}}"
      - "{{BaseURL}}/favicon.ico"
      - "{{BaseURL}}/images/favicon.ico"

    stop-at-first-match: true
    matchers-condition: or
    matchers:
      - type: dsl
        dsl:
          - "status_code==200 && (\"**********\" == mmh3(base64_py(body)))"

      - type: word
        words:
          - '<title>Procore Log In</title>'
          - 'Log in to your Procore account using your email and password'
        condition: and
# digest: 4b0a00483046022100a70746437be35ad2834e32ca6a7b98f64ea061191273f5c80765356a6be22734022100844d41c43167c7019b501794659223fda72f1e2f4121a285dfba82be134777cc:922c64590222798bb761d5b6d8e72950