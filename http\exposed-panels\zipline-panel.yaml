id: zipline-panel

info:
  name: <PERSON><PERSON> <PERSON>ipline - Detect
  author: icarot
  severity: info
  description: |
    Zipline panel was detected.
  reference:
    - https://github.com/diced/zipline
  metadata:
    verified: true
    max-request: 1
    vendor: diced
    product: zipline
    shodan-query: html:"Zipline"
  tags: diced,zipline,login,panel,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}/auth/login"

    matchers-condition: and
    matchers:
      - type: word
        words:
          - '"title":"Zipline"'
          - 'Zipline – Login</title>'
        condition: or
        case-insensitive: true

      - type: status
        status:
          - 200
# digest: 4a0a004730450221009da2edf02675f38581113e8d46c1b317fe62e671f125b134cfadf267308de8cc02200b65a6b4d0b193fda29e214f391fe476f22bb7375494c9e1dcbd84c1a0d38582:922c64590222798bb761d5b6d8e72950