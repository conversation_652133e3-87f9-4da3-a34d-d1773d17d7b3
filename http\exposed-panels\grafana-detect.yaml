id: grafana-detect

info:
  name: <PERSON><PERSON> Panel - Detect
  author: <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,bhutch
  severity: info
  description: Grafana login panel was detected.
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N
    cwe-id: CWE-200
    cpe: cpe:2.3:a:grafana:grafana:*:*:*:*:*:*:*:*
  metadata:
    max-request: 2
    vendor: grafana
    product: grafana
    shodan-query:
      - title:"Grafana"
      - cpe:"cpe:2.3:a:grafana:grafana"
      - http.title:"grafana"
    category: devops
    fofa-query:
      - title="grafana"
      - app="grafana"
    google-query: intitle:"grafana"
  tags: panel,grafana,detect

http:
  - method: GET
    path:
      - "{{BaseURL}}/login"
      - "{{BaseURL}}/graph/login"

    stop-at-first-match: true
    matchers:
      - type: word
        part: body
        words:
          - "<title><PERSON>ana</title>"

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - '\"version\"\:\"([0-9.]+)\"}'
          - '\"subTitle\":\"Grafana v([0-9.]+)'

      - type: kval
        kval:
          - version
# digest: 4b0a00483046022100c4051b13610587374a026cff239d2536309594bc56eecbae999dc2839f165877022100e2c648db89a5fd75395661d8bfecfb4d77f32d9543ad75f1bcf643265a34054e:922c64590222798bb761d5b6d8e72950