id: adfinity-panel

info:
  name: Adfinity Login Panel - Detect
  author: righettod
  severity: info
  description: |
    Adfinity products was detected.
  reference:
    - https://easi.net/en/solutions/adfinity
  metadata:
    max-request: 1
    verified: true
    shodan-query: http.html:"Adfinity"
  tags: panel,adfinity,login

http:
  - method: GET
    path:
      - "{{BaseURL}}/adfinity/login"

    matchers:
      - type: dsl
        dsl:
          - 'status_code == 200'
          - 'contains_any(to_lower(body), "adfinity web application", "adfinity web access", "csrf_adfinity_token")'
        condition: and

    extractors:
      - type: regex
        part: body
        group: 1
        regex:
          - '(?i)Version\s+Adfinity([0-9.]+)'
# digest: 4a0a0047304502202b9957a40cf5d2b381dad80db0b44f3e6705e3b399f24196e158f8bd116b4e77022100fd4bba86e35859e8b24038ae367261cb927e49981e88040369f3e9b412112b61:922c64590222798bb761d5b6d8e72950