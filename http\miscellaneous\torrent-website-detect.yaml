id: torrent-magnet-detect

info:
  name: Torrent Magnet - Detect
  author: rxerium
  severity: info
  description: |
    Detects magnet links present on a website, which are commonly used for torrenting.
  reference:
    - https://www.zdnet.com/article/what-is-torrenting-and-how-does-it-work/
  metadata:
    verified: true
    max-request: 1
  tags: torrent,website,detect,piracy,osint

http:
  - method: GET
    path:
      - "{{BaseURL}}"

    max-redirects: 1
    redirects: true

    matchers-condition: and
    matchers:
      - type: word
        words:
          - "magnet:?xt=urn:btih:"

      - type: status
        status:
          - 200

    extractors:
      - type: regex
        name: magnet-link
        part: body
        regex:
          - "magnet:\\?xt=urn:btih:[a-zA-Z0-9]*[^\"'\\s<]*"
# digest: 4a0a00473045022100882a022ea0bea2bb88809eedcb340fef52b6d87321f3b698469f5dbf567b6232022070a3c5a727cf7a270dc99014f659cc918df9bc2b15e433408f4ec3eb26f9ba09:922c64590222798bb761d5b6d8e72950