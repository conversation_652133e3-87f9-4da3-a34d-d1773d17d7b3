name: CVE-2025-7775 PoC Testing

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install requests urllib3
        
    - name: Build Docker simulation environment
      run: |
        cd CVE-2025-7775-PoC
        docker build -t cve-2025-7775-sim .
        
    - name: Start simulation environment
      run: |
        docker run -d --name netscaler-sim -p 8080:80 -p 8443:443 cve-2025-7775-sim
        sleep 10
        
    - name: Test NetScaler detection
      run: |
        curl -f http://localhost:8080/logon/LogonPoint/index.html || exit 1
        curl -f http://localhost:8080/nitro/v1/config/nsversion || exit 1
        
    - name: Test Nuclei template
      run: |
        # Install Nuclei
        wget -q https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_2.9.15_linux_amd64.zip
        unzip -q nuclei_2.9.15_linux_amd64.zip
        chmod +x nuclei
        
        # Validate template
        ./nuclei -validate -t http/cves/2025/CVE-2025-7775.yaml
        
        # Test against simulation
        ./nuclei -t http/cves/2025/CVE-2025-7775.yaml -target http://localhost:8080 -debug
        
    - name: Run PoC exploit script
      run: |
        cd CVE-2025-7775-PoC/scripts
        python3 exploit.py -t http://localhost:8080 -v
        
    - name: Test log scanning
      run: |
        cd CVE-2025-7775-PoC/scripts
        python3 log_scanner.py --log-file /tmp/test.log --scan-type cve-2025-7775
        
    - name: Collect logs and artifacts
      if: always()
      run: |
        docker logs netscaler-sim > simulation-logs.txt
        docker exec netscaler-sim cat /var/log/netscaler-sim.log > netscaler-sim.log || true
        
    - name: Upload artifacts
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: poc-test-results
        path: |
          simulation-logs.txt
          netscaler-sim.log
          
    - name: Cleanup
      if: always()
      run: |
        docker stop netscaler-sim || true
        docker rm netscaler-sim || true

  security-scan:
    runs-on: ubuntu-latest
    needs: build-and-test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run security scan on Docker image
      run: |
        cd CVE-2025-7775-PoC
        docker build -t cve-2025-7775-sim .
        
        # Install Trivy for security scanning
        wget -q https://github.com/aquasecurity/trivy/releases/latest/download/trivy_0.45.0_Linux-64bit.tar.gz
        tar zxf trivy_0.45.0_Linux-64bit.tar.gz
        
        # Scan the Docker image
        ./trivy image --exit-code 0 --severity HIGH,CRITICAL cve-2025-7775-sim
        
    - name: Validate template security
      run: |
        # Check for any potentially dangerous patterns in the template
        grep -n "command\|exec\|eval\|system" http/cves/2025/CVE-2025-7775.yaml || true
        
        # Ensure template is detection-only
        if grep -q "exploit\|shell\|reverse" http/cves/2025/CVE-2025-7775.yaml; then
          echo "ERROR: Template contains exploitation code"
          exit 1
        fi
